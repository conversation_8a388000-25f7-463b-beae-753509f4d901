import os
import json
import shutil
import zipfile
import logging
import datetime
from pathlib import Path
from jinja2 import Template
import traceback

logger = logging.getLogger(__name__)

class CustomReportGenerator:
    """
    Generates a custom HTML report for test execution and packages it as a ZIP file.
    The ZIP includes:
    - Custom HTML report
    - Screenshots
    - Action logs
    - Any JS/CSS required for the HTML report
    """
    
    def __init__(self, report_id, app_root_path):
        """
        Initialize the report generator
        
        Args:
            report_id (str): The ID of the report to generate
            app_root_path (str): The root path of the Flask app
        """
        self.report_id = report_id
        self.app_root_path = app_root_path
        self.source_report_dir = None
        self.export_dir = None
        self.export_report_path = None
        self.screenshots_dir = None
        self.template_path = os.path.join(app_root_path, 'app', 'templates', 'custom_report_template.html')
        
        # Setup directories
        self._setup_directories()
        
    def _setup_directories(self):
        """
        Set up the required directories for export
        """
        # Get the reports directory using the utility function
        try:
            from app.utils.directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            logger.info(f"Using reports directory from utility function: {reports_dir}")
        except Exception as e:
            # Fallback to default path
            reports_dir = os.path.join(self.app_root_path, '..', 'reports')
            reports_dir = os.path.abspath(reports_dir)
            logger.info(f"Using fallback reports directory: {reports_dir}")
        
        # Also check the alternate reports directory
        alt_reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
            logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
        
        # For reports with format "testsuite_execution_TIMESTAMP", the report ID is the entire directory name
        # First, try direct match in reports directory
        possible_dirs = []
        
        # First search method: exact directory name match
        exact_dir = os.path.join(reports_dir, self.report_id)
        if os.path.isdir(exact_dir):
            logger.info(f"Found exact match directory: {exact_dir}")
            self.source_report_dir = exact_dir
        
        # Second search method: report_id might be just the directory name, but real path is under reports/
        elif os.path.isdir(reports_dir):
            for entry in os.listdir(reports_dir):
                entry_path = os.path.join(reports_dir, entry)
                if os.path.isdir(entry_path) and self.report_id in entry:
                    possible_dirs.append(entry_path)
                    logger.info(f"Found possible matching directory: {entry_path}")
        
        # Third search method: check alternate reports directory
        if not self.source_report_dir and os.path.isdir(alt_reports_dir):
            alt_exact_dir = os.path.join(alt_reports_dir, self.report_id)
            if os.path.isdir(alt_exact_dir):
                logger.info(f"Found exact match in alternate directory: {alt_exact_dir}")
                self.source_report_dir = alt_exact_dir
                reports_dir = alt_reports_dir  # Use alternate directory for exports too
            else:
                for entry in os.listdir(alt_reports_dir):
                    entry_path = os.path.join(alt_reports_dir, entry)
                    if os.path.isdir(entry_path) and self.report_id in entry:
                        possible_dirs.append(entry_path)
                        logger.info(f"Found possible matching directory in alternate location: {entry_path}")
        
        # If we didn't find an exact match but have possible matches, use the first one
        if not self.source_report_dir and possible_dirs:
            self.source_report_dir = possible_dirs[0]
            logger.info(f"Using first matching directory: {self.source_report_dir}")
            # Also update reports_dir to the parent directory
            reports_dir = os.path.dirname(self.source_report_dir)
        
        # If we still don't have a source directory, try looking at the report URL format
        if not self.source_report_dir:
            # Check if this is a URL path like /reports/testsuite_execution_TIMESTAMP/mainreport.html
            if '/' in self.report_id:
                path_parts = self.report_id.split('/')
                if len(path_parts) >= 2:
                    # Try to extract the timestamp part
                    for part in path_parts:
                        if part.startswith('testsuite_execution_'):
                            dir_name = part
                            check_dir = os.path.join(reports_dir, dir_name)
                            if os.path.isdir(check_dir):
                                self.source_report_dir = check_dir
                                logger.info(f"Found report directory from URL path: {check_dir}")
                                break
                            
                            # Also check alternate directory
                            alt_check_dir = os.path.join(alt_reports_dir, dir_name)
                            if os.path.isdir(alt_check_dir):
                                self.source_report_dir = alt_check_dir
                                reports_dir = alt_reports_dir
                                logger.info(f"Found report directory from URL path in alternate location: {alt_check_dir}")
                                break
        
        # If we still don't have a report directory, search for test_data.json files
        if not self.source_report_dir:
            logger.info("Searching for test_data.json files containing the report ID")
            for search_dir in [reports_dir, alt_reports_dir]:
                if os.path.isdir(search_dir):
                    for root, dirs, files in os.walk(search_dir):
                        if 'test_data.json' in files or 'data.json' in files:
                            data_file = 'test_data.json' if 'test_data.json' in files else 'data.json'
                            try:
                                with open(os.path.join(root, data_file), 'r') as f:
                                    data = json.load(f)
                                    if 'report_id' in data and self.report_id in data['report_id']:
                                        self.source_report_dir = root
                                        reports_dir = os.path.dirname(root)
                                        logger.info(f"Found report directory via {data_file}: {root}")
                                        break
                            except Exception as e:
                                logger.warning(f"Error reading {data_file} at {os.path.join(root, data_file)}: {e}")
                if self.source_report_dir:
                    break
        
        # Last resort: The report ID might be the full URL, try extracting just the directory name
        if not self.source_report_dir and self.report_id.startswith('/reports/'):
            parts = self.report_id.strip('/').split('/')
            if len(parts) > 1:
                # The second part should be the directory name
                dir_name = parts[1]
                check_dir = os.path.join(reports_dir, dir_name)
                if os.path.isdir(check_dir):
                    self.source_report_dir = check_dir
                    logger.info(f"Found report directory from full URL: {check_dir}")
                # Also check alternate directory
                alt_check_dir = os.path.join(alt_reports_dir, dir_name)
                if os.path.isdir(alt_check_dir):
                    self.source_report_dir = alt_check_dir
                    reports_dir = alt_reports_dir
                    logger.info(f"Found report directory from full URL in alternate location: {alt_check_dir}")
        
        # If we still don't have a source directory, report the error
        if not self.source_report_dir:
            error_msg = f"Report directory for {self.report_id} not found in any location"
            logger.error(error_msg)
            # Dump all report directories for debugging
            if os.path.isdir(reports_dir):
                logger.error(f"Available directories in {reports_dir}:")
                for d in os.listdir(reports_dir):
                    if os.path.isdir(os.path.join(reports_dir, d)):
                        logger.error(f"  - {d}")
            if os.path.isdir(alt_reports_dir):
                logger.error(f"Available directories in {alt_reports_dir}:")
                for d in os.listdir(alt_reports_dir):
                    if os.path.isdir(os.path.join(alt_reports_dir, d)):
                        logger.error(f"  - {d}")
            raise FileNotFoundError(error_msg)
        
        logger.info(f"Found source report directory: {self.source_report_dir}")
        
        # Create export directory
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.export_dir = os.path.join(reports_dir, f"export_{os.path.basename(self.source_report_dir)}_{timestamp}")
        os.makedirs(self.export_dir, exist_ok=True)
        
        # Create screenshots directory within export
        self.screenshots_dir = os.path.join(self.export_dir, 'screenshots')
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # Set the path for the final HTML report
        self.export_report_path = os.path.join(self.export_dir, 'test_execution_report.html')
        
        logger.info(f"Export directory set up at: {self.export_dir}")
        logger.info(f"Export report will be saved to: {self.export_report_path}")
    
    def _load_test_data(self):
        """
        Load test data from the report directory
        
        Returns:
            dict: Test data JSON or empty dict if not found
        """
        # Check for test_data.json first
        test_data_path = os.path.join(self.source_report_dir, 'test_data.json')
        if os.path.exists(test_data_path):
            logger.info(f"Loading test data from: {test_data_path}")
            try:
                with open(test_data_path, 'r') as f:
                    test_data = json.load(f)
                logger.info(f"Successfully loaded test data from test_data.json: {len(test_data)} keys found")
                return test_data
            except Exception as e:
                logger.error(f"Error loading test_data.json: {e}")
        
        # Fall back to data.json if test_data.json doesn't exist
        data_json_path = os.path.join(self.source_report_dir, 'data.json')
        if os.path.exists(data_json_path):
            logger.info(f"Loading test data from fallback: {data_json_path}")
            try:
                with open(data_json_path, 'r') as f:
                    test_data = json.load(f)
                logger.info(f"Successfully loaded test data from data.json: {len(test_data)} keys found")
                return test_data
            except Exception as e:
                logger.error(f"Error loading data.json: {e}")
                
        logger.warning(f"No test data files found in: {self.source_report_dir}")
        return {}
    
    def _copy_screenshots(self):
        """
        Copy screenshots from the source report to the export directory
        
        Returns:
            int: Number of screenshots copied
        """
        source_screenshots_dir = os.path.join(self.source_report_dir, 'screenshots')
        logger.info(f"Copying screenshots from {source_screenshots_dir} to {self.screenshots_dir}")
        
        # Create screenshots directory if it doesn't exist
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # List of directories to search for screenshots
        screenshot_dirs = []
        
        # Check primary location
        if os.path.exists(source_screenshots_dir):
            screenshot_dirs.append(source_screenshots_dir)
        else:
            logger.warning(f"Source screenshots directory not found: {source_screenshots_dir}")
        
        # Try to find screenshots in the parent directory
        parent_dir = os.path.dirname(self.source_report_dir)
        parent_screenshots_dir = os.path.join(parent_dir, 'screenshots')
        if os.path.exists(parent_screenshots_dir):
            screenshot_dirs.append(parent_screenshots_dir)
            logger.info(f"Added parent screenshots directory: {parent_screenshots_dir}")
        
        # Try app static screenshots directory
        app_screenshots_dir = os.path.join(self.app_root_path, 'static', 'screenshots')
        if os.path.exists(app_screenshots_dir):
            screenshot_dirs.append(app_screenshots_dir)
            logger.info(f"Added app static screenshots directory: {app_screenshots_dir}")
            
        # Try temp screenshots directory
        temp_screenshots_dir = os.path.join(os.path.dirname(self.app_root_path), 'temp_screenshots')
        if os.path.exists(temp_screenshots_dir):
            screenshot_dirs.append(temp_screenshots_dir)
            logger.info(f"Added temp screenshots directory: {temp_screenshots_dir}")
            
        # Try global screenshots directory
        global_screenshots_dir = os.path.join(os.path.dirname(self.app_root_path), 'screenshots')
        if os.path.exists(global_screenshots_dir):
            screenshot_dirs.append(global_screenshots_dir)
            logger.info(f"Added global screenshots directory: {global_screenshots_dir}")
            
        # Try static/screenshots directory
        static_screenshots_dir = os.path.join(os.path.dirname(self.app_root_path), 'static', 'screenshots')
        if os.path.exists(static_screenshots_dir):
            screenshot_dirs.append(static_screenshots_dir)
            logger.info(f"Added static/screenshots directory: {static_screenshots_dir}")
        
        # If no screenshot directories found, return 0
        if not screenshot_dirs:
            logger.warning("No screenshots directories found")
            return 0
        
        # Get the list of action IDs from the test data to look for specific screenshots
        action_ids = set()
        test_data = self._load_test_data()
        
        # Extract action IDs from testCases structure
        if 'testCases' in test_data:
            for tc in test_data.get('testCases', []):
                for step in tc.get('steps', []):
                    action_id = step.get('action_id')
                    if action_id:
                        action_ids.add(action_id)
        
        # Extract action IDs from actions array
        if 'actions' in test_data:
            for action in test_data.get('actions', []):
                action_id = action.get('action_id')
                if action_id:
                    action_ids.add(action_id)
                    
        # Extract action IDs from test_cases structure (old format)
        if 'test_cases' in test_data:
            for tc in test_data.get('test_cases', []):
                steps = tc.get('steps', tc.get('actions', []))
                for step in steps:
                    action_id = step.get('action_id')
                    if action_id:
                        action_ids.add(action_id)
        
        # Create a map to hold all actual action_ids from the test data
        # Log first 10 for debugging
        sorted_action_ids = sorted(list(action_ids))
        if sorted_action_ids:
            sample = sorted_action_ids[:min(10, len(sorted_action_ids))]
            logger.info(f"Looking for screenshots for {len(action_ids)} action IDs. Sample: {', '.join(sample)}")
        else:
            logger.warning("No action IDs found in test data")
        
        # Create an action_id to index map for consistent ordering
        action_id_map = {}
        for idx, action_id in enumerate(sorted_action_ids):
            action_id_map[action_id] = idx + 1
        
        # Copy all screenshots from all directories
        count = 0
        copied_files = set()  # Track which files have been copied
        
        # Map to track which actions have screenshots
        action_screenshots = {}
        
        # First pass: copy screenshots with exact action_id filenames
        for action_id in action_ids:
            screenshot_filename = f"{action_id}.png"
            found = False
            
            for source_dir in screenshot_dirs:
                src_path = os.path.join(source_dir, screenshot_filename)
                if os.path.exists(src_path):
                    dst_path = os.path.join(self.screenshots_dir, screenshot_filename)
                    try:
                        shutil.copy2(src_path, dst_path)
                        copied_files.add(screenshot_filename)
                        count += 1
                        logger.info(f"Copied action screenshot: {screenshot_filename}")
                        action_screenshots[action_id] = screenshot_filename
                        found = True
                        break
                    except Exception as e:
                        logger.error(f"Error copying screenshot {screenshot_filename}: {e}")
            
            if not found:
                # Second pass: look for filenames containing the action_id
                for source_dir in screenshot_dirs:
                    if not os.path.exists(source_dir):
                        continue
                        
                    for filename in os.listdir(source_dir):
                        if action_id in filename and filename.endswith(('.png', '.jpg', '.jpeg')):
                            src_path = os.path.join(source_dir, filename)
                            dst_path = os.path.join(self.screenshots_dir, screenshot_filename)
                            try:
                                shutil.copy2(src_path, dst_path)
                                copied_files.add(screenshot_filename)
                                count += 1
                                logger.info(f"Found and copied partial match for action ID {action_id}: {filename}")
                                action_screenshots[action_id] = screenshot_filename
                                found = True
                                break
                            except Exception as e:
                                logger.error(f"Error copying partial match screenshot for {action_id}: {e}")
                    
                    if found:
                        break
                
                # If still not found, create a placeholder with sequential numbering
                if not found:
                    # Create a placeholder using a generic successful image if available
                    placeholder_path = os.path.join(self.screenshots_dir, screenshot_filename)
                    try:
                        # Try to use successful.png as a placeholder if available
                        placeholder_found = False
                        for source_dir in screenshot_dirs:
                            success_path = os.path.join(source_dir, 'successful.png')
                            if os.path.exists(success_path):
                                shutil.copy2(success_path, placeholder_path)
                                logger.info(f"Created placeholder for {action_id} using successful.png")
                                placeholder_found = True
                                count += 1
                                copied_files.add(screenshot_filename)
                                action_screenshots[action_id] = screenshot_filename
                                break
                            
                        # If we can't find successful.png, try other common images
                        if not placeholder_found:
                            # Look for any png files to use as placeholders
                            for source_dir in screenshot_dirs:
                                if os.path.exists(source_dir):
                                    png_files = [f for f in os.listdir(source_dir) if f.endswith('.png')]
                                    if png_files:
                                        src_path = os.path.join(source_dir, png_files[0])
                                        shutil.copy2(src_path, placeholder_path)
                                        logger.info(f"Created placeholder for {action_id} using {png_files[0]}")
                                        placeholder_found = True
                                        count += 1
                                        copied_files.add(screenshot_filename)
                                        action_screenshots[action_id] = screenshot_filename
                                        break
                                        
                        # Last resort: create a text file placeholder
                        if not placeholder_found:
                            # Create an empty text file as placeholder
                            with open(placeholder_path, 'w') as f:
                                f.write(f"Placeholder for action ID: {action_id}")
                            logger.info(f"Created text placeholder for {action_id}")
                            count += 1
                            copied_files.add(screenshot_filename)
                            action_screenshots[action_id] = screenshot_filename
                    except Exception as e:
                        logger.error(f"Error creating placeholder for {action_id}: {e}")
        
        # Copy custom screenshots for takeScreenshot actions
        custom_count = self._copy_custom_screenshots(test_data)
        count += custom_count

        # Now let's update the test_data with correct screenshot information
        self._update_test_data_screenshots(test_data, action_screenshots)

        logger.info(f"Copied {count} screenshots")
        return count

    def _copy_custom_screenshots(self, test_data):
        """
        Copy custom screenshots for takeScreenshot actions

        Args:
            test_data (dict): Test data containing takeScreenshot actions

        Returns:
            int: Number of custom screenshots copied
        """
        count = 0

        # Get source screenshot directories
        screenshot_dirs = [
            os.path.join(self.source_report_dir, 'screenshots'),
            os.path.join(self.source_report_dir, '..', 'screenshots'),
            os.path.join(self.source_report_dir, '..', '..', 'screenshots')
        ]

        # Find takeScreenshot actions with custom screenshot names
        custom_screenshots = set()

        # Check testCases format
        if 'testCases' in test_data:
            for tc in test_data.get('testCases', []):
                for step in tc.get('steps', []):
                    step_name = step.get('name', '').lower()
                    step_description = step.get('description', '').lower()
                    step_type = step.get('type', '').lower()
                    if 'takescreenshot' in step_name or 'takescreenshot' in step_description or step_type == 'takescreenshot':
                        screenshot_name = step.get('screenshot_name', '')
                        if screenshot_name:
                            custom_screenshots.add(screenshot_name)

        # Check test_cases format
        if 'test_cases' in test_data:
            for tc in test_data.get('test_cases', []):
                steps = tc.get('steps', tc.get('actions', []))
                for step in steps:
                    step_name = step.get('name', '').lower()
                    step_description = step.get('description', '').lower()
                    step_type = step.get('type', '').lower()
                    if 'takescreenshot' in step_name or 'takescreenshot' in step_description or step_type == 'takescreenshot':
                        screenshot_name = step.get('screenshot_name', '')
                        if screenshot_name:
                            custom_screenshots.add(screenshot_name)

        # Copy each custom screenshot
        for screenshot_name in custom_screenshots:
            screenshot_filename = f"{screenshot_name}.png"
            found = False

            for source_dir in screenshot_dirs:
                src_path = os.path.join(source_dir, screenshot_filename)
                if os.path.exists(src_path):
                    dst_path = os.path.join(self.screenshots_dir, screenshot_filename)
                    try:
                        shutil.copy2(src_path, dst_path)
                        count += 1
                        logger.info(f"Copied custom screenshot: {screenshot_filename}")
                        found = True
                        break
                    except Exception as e:
                        logger.error(f"Error copying custom screenshot {screenshot_filename}: {e}")

            if not found:
                logger.warning(f"Custom screenshot not found: {screenshot_filename}")

        logger.info(f"Copied {count} custom screenshots")
        return count

    def _update_test_data_screenshots(self, test_data, action_screenshots):
        """
        Update the test data with correct screenshot paths
        
        Args:
            test_data (dict): Test data to update
            action_screenshots (dict): Map of action IDs to screenshot filenames
        """
        if not action_screenshots:
            logger.warning("No action screenshots to update in test data")
            return
            
        # Update in testCases format
        if 'testCases' in test_data:
            for tc in test_data.get('testCases', []):
                for step in tc.get('steps', []):
                    action_id = step.get('action_id')
                    if action_id and action_id in action_screenshots:
                        step['screenshot'] = f"screenshots/{action_screenshots[action_id]}"
                        logger.info(f"Updated step screenshot to {step['screenshot']} for action ID {action_id}")
                        
        # Update in test_cases format (old format)
        if 'test_cases' in test_data:
            for tc in test_data.get('test_cases', []):
                steps = tc.get('steps', tc.get('actions', []))
                for step in steps:
                    action_id = step.get('action_id')
                    if action_id and action_id in action_screenshots:
                        step['screenshot'] = f"screenshots/{action_screenshots[action_id]}"
                        logger.info(f"Updated step screenshot to {step['screenshot']} for action ID {action_id}")
        
        logger.info(f"Updated {len(action_screenshots)} screenshot references in test data")
    
    def _copy_action_logs(self):
        """
        Copy action logs from the source report to the export directory
        
        Returns:
            bool: True if logs were copied successfully
        """
        source_logs_path = os.path.join(self.source_report_dir, 'action_logs.txt')
        dest_logs_path = os.path.join(self.export_dir, 'action_logs.txt')
        
        logger.info(f"Copying action logs from {source_logs_path} to {dest_logs_path}")
        
        if not os.path.exists(source_logs_path):
            logger.warning(f"Action logs file not found: {source_logs_path}")
            # Create an empty file
            with open(dest_logs_path, 'w') as f:
                f.write("No action logs available for this test run.")
            return False
        
        try:
            shutil.copy2(source_logs_path, dest_logs_path)
            logger.info("Action logs copied successfully")
            return True
        except Exception as e:
            logger.error(f"Error copying action logs: {e}")
            return False
    
    def generate_report(self):
        """
        Generate the custom HTML report
        
        Returns:
            bool: True if report was generated successfully
        """
        logger.info(f"Generating custom HTML report for {self.report_id}")
        
        # Load test data
        test_data = self._load_test_data()
        if not test_data:
            logger.error("No test data available, cannot generate report")
            return False
        
        # Log the test data structure for debugging
        logger.info(f"Test data structure: {json.dumps(test_data)[:500]}...")
        
        # Copy screenshots
        screenshot_count = self._copy_screenshots()
        logger.info(f"Copied {screenshot_count} screenshots")
        
        # Copy action logs
        self._copy_action_logs()
        
        # Process test data for the template
        processed_test_data = self._process_test_data(test_data)
        
        # Load template
        try:
            with open(self.template_path, 'r') as f:
                template_content = f.read()
                
            template = Template(template_content)
            
            # Prepare template context
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Count passed and failed tests
            test_cases = processed_test_data['test_cases']
            
            context = {
                'timestamp': timestamp,
                'report_id': self.report_id,
                'total_test_cases': len(test_cases),
                'passed_test_cases': sum(1 for tc in test_cases if tc.get('status') == 'Passed'),
                'failed_test_cases': sum(1 for tc in test_cases if tc.get('status') == 'Failed'),
                'test_cases': test_cases
            }
            
            # Render template
            html_content = template.render(**context)
            
            # Write to file
            with open(self.export_report_path, 'w') as f:
                f.write(html_content)
                
            logger.info(f"Custom HTML report generated successfully at {self.export_report_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _process_test_data(self, test_data):
        """
        Process the test data into a format expected by the template
        
        Args:
            test_data (dict): The raw test data loaded from JSON
            
        Returns:
            dict: Processed test data ready for the template
        """
        processed_data = {'test_cases': []}
        
        # Log the keys in test_data for debugging
        logger.info(f"Test data keys: {list(test_data.keys())}")
        
        # First try: testCases format (new format)
        if 'testCases' in test_data:
            test_cases = test_data['testCases']
            logger.info(f"Found {len(test_cases)} test cases in 'testCases' format")
            
            for idx, tc in enumerate(test_cases):
                processed_tc = {
                    'name': tc.get('name', f'Test Case {idx+1}'),
                    'status': 'Passed' if tc.get('status') == 'passed' else 'Failed' if tc.get('status') == 'failed' else 'Unknown',
                    'actions': []
                }
                
                steps = tc.get('steps', [])
                logger.info(f"Test case '{processed_tc['name']}' has {len(steps)} steps")
                
                for step_idx, step in enumerate(steps):
                    action_type = self._determine_action_type(step)
                    action = {
                        'index': step_idx + 1,
                        'type': action_type,
                        'description': step.get('name', 'Unknown action')
                    }
                    
                    # Add screenshot if available - ALWAYS prioritize action_id for consistency
                    action_id = step.get('action_id', '')
                    if action_id:
                        # For takeScreenshot actions, try to use the custom screenshot name first
                        step_name = step.get('name', '').lower()
                        step_description = step.get('description', '').lower()
                        step_type = step.get('type', '').lower()
                        if 'takescreenshot' in step_name or 'takescreenshot' in step_description or step_type == 'takescreenshot' or action_type.lower() == 'takescreenshot':
                            # Try to find the custom screenshot name from the step parameters
                            screenshot_name = step.get('screenshot_name', '')
                            if screenshot_name:
                                custom_screenshot_path = f"screenshots/{screenshot_name}.png"
                                # Check in the source report directory, not the export directory
                                source_custom_screenshot_file = os.path.join(self.source_report_dir, "screenshots", f"{screenshot_name}.png")
                                if os.path.exists(source_custom_screenshot_file):
                                    logger.info(f"Found custom screenshot for takeScreenshot action: {custom_screenshot_path}")
                                    action['screenshot'] = custom_screenshot_path
                                else:
                                    # Fallback to action_id screenshot
                                    screenshot_path = f"screenshots/{action_id}.png"
                                    logger.info(f"Custom screenshot not found, using action_id screenshot: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                            else:
                                # No custom name, use action_id
                                screenshot_path = f"screenshots/{action_id}.png"
                                action['screenshot'] = screenshot_path
                        else:
                            # Always use action_id.png format for screenshot path
                            screenshot_path = f"screenshots/{action_id}.png"
                            screenshot_file = os.path.join(self.screenshots_dir, f"{action_id}.png")

                            # Check if the screenshot file actually exists
                            if os.path.exists(screenshot_file):
                                logger.info(f"Found screenshot for action ID {action_id}: {screenshot_path}")
                                action['screenshot'] = screenshot_path
                            else:
                                # Check if step already has a screenshot field with a usable path
                                if 'screenshot' in step and step['screenshot']:
                                    # Extract just the filename from the path
                                    screenshot_filename = os.path.basename(step['screenshot'])
                                    screenshot_path = f"screenshots/{screenshot_filename}"
                                    logger.info(f"Using existing screenshot reference from step: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                                else:
                                    # Use generic path based on action ID
                                    logger.info(f"Using generic screenshot path for action ID {action_id}: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                    else:
                        # Fallback to other fields only if action_id is not available
                        for field in ['screenshot', 'screenshot_filename', 'screenshot_path', 'resolved_screenshot']:
                            if field in step and step[field]:
                                # Extract just the filename if it's a full path
                                path = step[field]
                                filename = os.path.basename(path)
                                screenshot_path = f"screenshots/{filename}"
                                logger.info(f"Using {field} for screenshot: {screenshot_path}")
                                action['screenshot'] = screenshot_path
                                break
                    
                    processed_tc['actions'].append(action)
                    
                processed_data['test_cases'].append(processed_tc)
                
        # Second try: test_cases format (old format)
        elif 'test_cases' in test_data:
            test_cases = test_data['test_cases']
            logger.info(f"Found {len(test_cases)} test cases in 'test_cases' format")
            
            for idx, tc in enumerate(test_cases):
                processed_tc = {
                    'name': tc.get('name', f'Test Case {idx+1}'),
                    'status': tc.get('status', 'Unknown'),
                    'actions': []
                }
                
                # Map different possible step containers
                steps = tc.get('steps', tc.get('actions', []))
                logger.info(f"Test case '{processed_tc['name']}' has {len(steps)} steps")
                
                for step_idx, step in enumerate(steps):
                    action_type = self._determine_action_type(step)
                    action = {
                        'index': step_idx + 1,
                        'type': action_type,
                        'description': step.get('description', step.get('name', 'Unknown action'))
                    }

                    # Add screenshot if available - ALWAYS prioritize action_id for consistency
                    action_id = step.get('action_id', '')
                    if action_id:
                        # For takeScreenshot actions, try to use the custom screenshot name first
                        step_name = step.get('name', '').lower()
                        step_description = step.get('description', '').lower()
                        step_type = step.get('type', '').lower()
                        if 'takescreenshot' in step_name or 'takescreenshot' in step_description or step_type == 'takescreenshot' or action_type.lower() == 'takescreenshot':
                            # Try to find the custom screenshot name from the step parameters
                            screenshot_name = step.get('screenshot_name', '')
                            if screenshot_name:
                                custom_screenshot_path = f"screenshots/{screenshot_name}.png"
                                # Check in the source report directory, not the export directory
                                source_custom_screenshot_file = os.path.join(self.source_report_dir, "screenshots", f"{screenshot_name}.png")
                                if os.path.exists(source_custom_screenshot_file):
                                    logger.info(f"Found custom screenshot for takeScreenshot action: {custom_screenshot_path}")
                                    action['screenshot'] = custom_screenshot_path
                                else:
                                    # Fallback to action_id screenshot
                                    screenshot_path = f"screenshots/{action_id}.png"
                                    logger.info(f"Custom screenshot not found, using action_id screenshot: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                            else:
                                # No custom name, use action_id
                                screenshot_path = f"screenshots/{action_id}.png"
                                action['screenshot'] = screenshot_path
                        else:
                            # Always use action_id.png format for screenshot path
                            screenshot_path = f"screenshots/{action_id}.png"
                            screenshot_file = os.path.join(self.screenshots_dir, f"{action_id}.png")

                            # Check if the screenshot file actually exists
                            if os.path.exists(screenshot_file):
                                logger.info(f"Found screenshot for action ID {action_id}: {screenshot_path}")
                                action['screenshot'] = screenshot_path
                            else:
                                # Check if step already has a screenshot field with a usable path
                                if 'screenshot' in step and step['screenshot']:
                                    # Extract just the filename from the path
                                    screenshot_filename = os.path.basename(step['screenshot'])
                                    screenshot_path = f"screenshots/{screenshot_filename}"
                                    logger.info(f"Using existing screenshot reference from step: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                                else:
                                    # Use generic path based on action ID
                                    logger.info(f"Using generic screenshot path for action ID {action_id}: {screenshot_path}")
                                    action['screenshot'] = screenshot_path
                    else:
                        # Fallback to other fields only if action_id is not available
                        for field in ['screenshot', 'screenshot_filename', 'screenshot_path']:
                            if field in step and step[field]:
                                # Extract just the filename if it's a full path
                                path = step[field]
                                filename = os.path.basename(path)
                                screenshot_path = f"screenshots/{filename}"
                                logger.info(f"Using {field} for screenshot: {screenshot_path}")
                                action['screenshot'] = screenshot_path
                                break
                    
                    processed_tc['actions'].append(action)
                    
                processed_data['test_cases'].append(processed_tc)
        
        # If no test cases found, create a dummy one for the report
        if not processed_data['test_cases']:
            logger.warning("No test cases found in data, creating placeholder")
            
            # Create a placeholder test case with information about the issue
            processed_data['test_cases'] = [{
                'name': 'Test Report Information',
                'status': 'Unknown',
                'actions': [{
                    'index': 1,
                    'type': 'info',
                    'description': 'No test case data found in the report. This may indicate an issue with the test execution or report generation.'
                }]
            }]
            
        return processed_data
        
    def _determine_action_type(self, action):
        """
        Determine the action type from the action data
        
        Args:
            action (dict): Action data
            
        Returns:
            str: Action type as a string
        """
        # Try to get type from various possible fields
        action_type = action.get('type', 
                     action.get('action_type',
                     action.get('actionType', '')))
        
        # If no type field, try to infer from the action name/description
        if not action_type:
            description = action.get('description', action.get('name', '')).lower()
            
            if 'tap' in description or 'click' in description:
                return 'tap'
            elif 'swipe' in description:
                return 'swipe'
            elif 'text' in description or 'type' in description:
                return 'text'
            elif 'wait' in description:
                return 'wait'
            elif 'launch' in description:
                return 'launchApp'
            elif 'terminate' in description or 'close' in description:
                return 'terminateApp'
            else:
                return 'action'
        
        # Clean up the action type (remove "action" suffix, convert to lowercase, etc.)
        action_type = action_type.lower()
        if action_type.endswith('action'):
            action_type = action_type[:-6]
            
        # Map to standard types used in the template
        type_mapping = {
            'tap': 'tap',
            'swipe': 'swipe',
            'text': 'text',
            'type': 'text',
            'wait': 'wait',
            'launch': 'launchApp',
            'terminate': 'terminateApp',
            'click': 'tap',
            'addlog': 'addLog',
            'takescreenshot': 'takeScreenshot'
        }
        
        return type_mapping.get(action_type, action_type)
    
    def create_zip(self):
        """
        Create a ZIP file of the export directory
        
        Returns:
            str: Path to the ZIP file or None if failed
        """
        zip_path = f"{self.export_dir}.zip"
        logger.info(f"Creating ZIP file at {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add files from export directory
                for root, dirs, files in os.walk(self.export_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Calculate relative path for the zip
                        rel_path = os.path.relpath(file_path, os.path.dirname(self.export_dir))
                        zipf.write(file_path, rel_path)
                        logger.debug(f"Added to ZIP: {rel_path}")
            
            logger.info(f"ZIP file created successfully at {zip_path}")
            return zip_path
        except Exception as e:
            logger.error(f"Error creating ZIP file: {e}")
            return None
    
    def cleanup(self):
        """
        Clean up temporary files
        """
        if self.export_dir and os.path.exists(self.export_dir):
            try:
                shutil.rmtree(self.export_dir)
                logger.info(f"Cleaned up export directory: {self.export_dir}")
            except Exception as e:
                logger.warning(f"Error cleaning up export directory: {e}")
    
    def generate_and_zip(self):
        """
        Generate report and create ZIP file
        
        Returns:
            tuple: (bool success, str zip_path)
        """
        try:
            # Generate the report
            if not self.generate_report():
                return False, None
            
            # Create ZIP file
            zip_path = self.create_zip()
            if not zip_path:
                return False, None
                
            return True, zip_path
        except Exception as e:
            logger.error(f"Error in generate_and_zip: {e}")
            return False, None

def generate_custom_report(report_id, app_root_path):
    """
    Generate a custom report for the given report ID
    
    Args:
        report_id (str): The ID of the report to generate
        app_root_path (str): The root path of the Flask app
        
    Returns:
        tuple: (bool success, str message, str zip_path)
    """
    logger.info(f"Starting custom report generation for report ID: {report_id}")
    
    try:
        generator = CustomReportGenerator(report_id, app_root_path)
        success, zip_path = generator.generate_and_zip()
        
        if success and zip_path:
            filename = os.path.basename(zip_path)
            return True, "Report generated successfully", filename
        else:
            return False, "Failed to generate report", None
    except Exception as e:
        logger.exception(f"Error generating custom report: {e}")
        return False, f"Error: {str(e)}", None 