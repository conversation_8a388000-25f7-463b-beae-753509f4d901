import os
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def build_data_json_from_test_case(test_case_file, suite_id, execution_data, success=True, error=None):
    """
    Build data.json directly from the test case JSON file

    Args:
        test_case_file (str): Path to the test case JSON file
        suite_id (str): Suite ID for the test execution
        execution_data (list): Execution tracking data from the database
        success (bool): Whether the test execution was successful
        error (str): Error message if the test execution failed

    Returns:
        dict: Data structure for data.json
    """
    try:
        # Load the test case JSON file
        test_case_path = test_case_file
        if not os.path.exists(test_case_path):
            test_case_path = os.path.join('test_cases', test_case_file)
        if not os.path.exists(test_case_path):
            test_case_path = os.path.join('test_cases', f"{test_case_file}.json")

        logger.info(f"Attempting to load test case from: {test_case_path}")
        with open(test_case_path, 'r') as f:
            test_case = json.load(f)

        logger.info(f"Loaded test case from {test_case_path}")

        # Create a mapping of step_idx to action_id from the test case
        test_case_action_ids = {}
        for idx, action in enumerate(test_case.get('actions', [])):
            action_id = action.get('action_id', '')
            if action_id:
                test_case_action_ids[idx] = action_id
                logger.info(f"Mapped step_idx {idx} to action_id {action_id} from test case")

        # Log all action_ids from the test case for debugging
        logger.info(f"Test case action_ids: {list(test_case_action_ids.values())}")

        # Create a mapping of step_idx to execution results
        execution_results = {}
        for entry in execution_data:
            step_idx = entry.get('step_idx')
            if step_idx is not None:
                execution_results[step_idx] = {
                    'status': entry.get('status', 'unknown'),
                    'timestamp': entry.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    'duration': entry.get('duration', '0ms'),
                    'action_id': entry.get('action_id', '')
                }
                logger.info(f"Added execution result for step_idx {step_idx}: {execution_results[step_idx]}")

        logger.info(f"Created execution results mapping with {len(execution_results)} entries")

        # Create steps from the test case actions
        steps = []
        for idx, action in enumerate(test_case.get('actions', [])):
            action_type = action.get('type', 'unknown')

            # IMPORTANT: Always use the action_id from the test case if available
            action_id = action.get('action_id', '')
            if not action_id:
                logger.warning(f"Action at index {idx} has no action_id, generating one")
                import random
                import string
                chars = string.ascii_letters + string.digits
                action_id = ''.join(random.choice(chars) for _ in range(10))
                logger.info(f"Generated action_id {action_id} for step {idx}")

            logger.info(f"Processing action {idx} with action_id {action_id} and type {action_type}")

            # Create a step name based on action type and any message
            step_name = f"Step {idx+1}: {action_type}"

            # For launchApp action
            if action_type == 'launchApp' and 'package' in action:
                step_name = f"Launch app: {action['package']}"

            # For terminateApp action
            elif action_type == 'terminateApp' and 'package' in action:
                step_name = f"Terminate app: {action['package']}"

            # For clickElement action
            elif action_type == 'clickElement' and 'locator_type' in action and 'locator_value' in action:
                step_name = f"Click element with {action['locator_type']} locator \"{action['locator_value']}\" (timeout: {action.get('timeout', 10)}s)"

            # For addLog action
            elif action_type == 'addLog' and 'message' in action:
                if action.get('take_screenshot', False):
                    step_name = f"{action['message']} (with screenshot)"
                else:
                    step_name = action['message']

            # For takeScreenshot action
            elif action_type == 'takeScreenshot':
                screenshot_name = action.get('screenshot_name', '')
                if screenshot_name:
                    step_name = f"takeScreenshot action: {screenshot_name}"
                else:
                    step_name = "takeScreenshot action"

            # Create a step from the action
            step = {
                'name': step_name,
                'action_type': action_type,
                'action_id': action_id,  # Use the original action_id from the test case
                'screenshot_filename': f"{action_id}.png",
                'report_screenshot': f"{action_id}.png",
                'resolved_screenshot': f"screenshots/{action_id}.png",
                'screenshot': f"screenshots/{action_id}.png",
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'duration': '0ms',
                'status': 'passed',  # Default to passed
                'action_id_screenshot': f"screenshots/{action_id}.png"
            }

            # Add action parameters
            for key, value in action.items():
                if key not in ['action_id', 'type']:
                    step[key] = value

            # Add clean_action_id and prefixed_action_id for consistency
            if action_id.startswith('al_'):
                step['clean_action_id'] = action_id[3:]  # Remove 'al_' prefix
                step['prefixed_action_id'] = action_id
            else:
                step['clean_action_id'] = action_id
                step['prefixed_action_id'] = f"al_{action_id}"

            # Add execution result if available
            if idx in execution_results:
                logger.info(f"Found execution result for step_idx {idx}")
                step['status'] = execution_results[idx]['status']
                step['timestamp'] = execution_results[idx]['timestamp']
                step['duration'] = execution_results[idx]['duration']

                # IMPORTANT: Never override the action_id from the test case
                # We always want to keep the original action_id for consistent screenshot mapping
                logger.info(f"Keeping original action_id {action_id} from test case for step {idx}")
            else:
                logger.warning(f"No execution result found for step_idx {idx}")

            steps.append(step)

        logger.info(f"Created {len(steps)} steps from test case actions")

        # Create screenshots map for the report
        screenshots_map = {}
        for idx, step in enumerate(steps):
            key = f"0_{idx}"
            screenshots_map[key] = step['screenshot_filename']
            logger.info(f"Added screenshot mapping: {key} -> {step['screenshot_filename']}")

        # Create the test suite data structure
        suite_data = {
            'name': f"Test Case: {os.path.basename(test_case_file)}",
            'id': suite_id,
            'testCases': [
                {
                    'name': os.path.basename(test_case_file),
                    'id': test_case_file,
                    'status': 'passed' if success else 'failed',
                    'duration': '0ms',  # We don't have accurate duration
                    'steps': steps
                }
            ],
            'status': 'passed' if success else 'failed',
            'passed': 1 if success else 0,
            'failed': 0 if success else 1,
            'skipped': 0,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': error if not success else None,
            'screenshots_map': screenshots_map
        }

        return suite_data
    except Exception as e:
        logger.error(f"Error building data.json from test case: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
