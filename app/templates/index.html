<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Automation Tool</title>
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/test-cases-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/test-suites-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/test-case.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/execution-overlay.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fixed-device-screen.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/actionStyles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <script src="{{ url_for('static', filename='js/modules/uiUtils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/actionFormManager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/reportAndFormUtils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/export-run.js') }}"></script>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Modern Header -->
        <div class="app-header">
            <h2>Mobile App Automation Tool</h2>
            <p>Create and execute automated mobile actions</p>
        </div>

        <!-- Nav tabs with improved styling -->
        <ul class="nav nav-tabs" id="main-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="device-tab" data-bs-toggle="tab" data-bs-target="#device-control-tab" type="button" role="tab">
                    <i class="bi bi-phone me-2"></i>Device Control
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="test-cases-tab-btn" data-bs-toggle="tab" data-bs-target="#test-cases-tab" type="button" role="tab">
                    <i class="bi bi-card-checklist me-2"></i>Test Cases
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="test-suites-tab-btn" data-bs-toggle="tab" data-bs-target="#test-suites-tab" type="button" role="tab">
                    <i class="bi bi-collection me-2"></i>Test Suites
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab-btn" data-bs-toggle="tab" data-bs-target="#settings-tab" type="button" role="tab">
                    <i class="bi bi-gear me-2"></i>Settings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="environments-tab-btn" data-bs-toggle="tab" data-bs-target="#environments-tab-pane" type="button" role="tab">
                    <i class="bi bi-hdd-stack me-2"></i>Environments
                </button>
            </li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content" style="position: relative;">
            <!-- Home tab (Device Control) -->
            <div class="tab-pane fade show active" id="device-control-tab" role="tabpanel" aria-labelledby="device-tab">
                <div class="row mt-3">
                    <!-- Left column for device screen and action log -->
                    <div class="col-md-5">
                        <!-- Card for device connection -->
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Device Connection</h5>
                            </div>
                            <div class="card-body">
                                <div class="input-group mb-3">
                                    <select id="device-selector" class="form-select" aria-label="Device selection">
                                        <option selected disabled>Select device</option>
                                    </select>
                                    <button id="refreshDevices" class="btn" type="button">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </button>
                                    <button id="connect-button" class="btn btn-primary" type="button">Connect</button>
                                </div>
                            </div>
                        </div>

                        <!-- Device Screen -->
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <div class="device-screen-header">
                                    <h5 class="device-screen-title">Device Screen</h5>
                                    <div class="device-screen-actions">
                                        <button id="refreshScreenBtn" class="btn btn-sm" data-requires-connection="true" disabled data-bs-toggle="tooltip" data-bs-placement="top" title="Screenshots are only refreshed manually or after actions for better performance">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh
                                        </button>
                                        <button id="webInspectorBtn" class="btn btn-sm" data-requires-connection="true" disabled data-bs-toggle="tooltip" data-bs-placement="top" title="Open Appium Web Inspector in a new window">
                                            <i class="bi bi-window"></i> Web Inspector
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body position-relative" style="min-height: 300px;">
                                <div id="loadingOverlay" class="position-absolute top-0 start-0 end-0 bottom-0 bg-dark d-none" style="opacity: 0.7; z-index: 100;">
                                    <div class="d-flex align-items-center justify-content-center h-100">
                                        <div class="text-center">
                                            <div class="spinner-border text-light mb-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="text-light" id="loadingMessage">Loading...</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="deviceScreenContainer" class="position-relative text-center">
                                    <img id="deviceScreen" src="static/img/no_device.png" class="img-fluid device-screen" alt="Device Screen">
                                    <canvas id="overlayCanvas" class="position-absolute top-0 start-0 d-none"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Action Log -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Action Log</h5>
                                <button class="btn btn-sm btn-outline-secondary" id="clearLogBtn">
                                    <i class="bi bi-trash"></i> Clear
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div id="actionLog" class="action-log"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Right column for action builder and actions list -->
                    <div class="col-md-7">
                        <!-- Action Builder Card -->
                        <div class="card mb-3 card-recording">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 action-builder-title">
                                    Action Builder
                                    <button class="btn btn-sm btn-link collapse-toggle" data-bs-toggle="collapse" data-bs-target="#actionBuilderCollapse" aria-expanded="true" aria-controls="actionBuilderCollapse">
                                        <i class="bi bi-chevron-up collapse-icon"></i>
                                    </button>
                                </h5>
                                <!-- Action buttons -->
                                <div class="d-flex justify-content-end mb-3">
                                    <button id="executeActions" class="btn btn-primary me-2" disabled>
                                        <i class="bi bi-play-fill"></i> Execute All
                                    </button>
                                    <button id="stopExecution" class="btn btn-danger me-2" disabled>
                                        <i class="bi bi-stop-fill"></i> Stop Execution
                                    </button>
                                    <button id="clearActions" class="btn btn-secondary" disabled>
                                        <i class="bi bi-trash"></i> Clear All
                                    </button>
                                </div>
                            </div>
                            <div id="actionBuilderCollapse" class="collapse show card-body">
                                <!-- Action Builder Form -->
                                <div class="action-builder">
                                    <div class="form-group mb-3">
                                        <label for="actionType">Action Type</label>
                                        <select id="actionType" class="form-control">
                                            <option value="tap">Tap</option>
                                            <option value="doubleTap">Double Tap</option>
                                            <option value="swipe">Swipe</option>
                                            <option value="text">Input Text</option>
                                            <option value="tapAndType">Tap and Type (iOS)</option>
                                            <option value="tapOnText">Tap on Text</option>
                                            <option value="sendKeys">Send Keys</option>
                                            <option value="addLog">Add Log</option>
                                            <option value="tapIfImageExists">Tap If Image Exists</option>
                                            <option value="key">Press Key</option>
                                            <option value="wait">Wait</option>
                                            <option value="hideKeyboard">Hide Keyboard</option>
                                            <option value="airplaneMode">Airplane Mode</option>
                                            <option value="addMedia">Add Media</option>
                                            <option value="deviceBack">Device Back (Android Only)</option>
                                            <option value="getValue">Get Value</option>
                                            <option value="compareValue">Compare Value</option>
                                            <option value="getParam">Get Parameter</option>
                                            <option value="setParam">Set Parameter</option>
                                            <option value="launchApp">Launch App</option>
                                            <option value="restartApp">Restart App</option>
                                            <option value="terminateApp">Terminate App</option>
                                            <option value="uninstallApp">Uninstall App</option>
                                            <option value="swipeTillVisible">Swipe Till Visible</option>
                                            <option value="waitTill">Wait Till Element</option>
                                            <option value="exists">Check If Exists</option>
                                            <option value="textClear">Clear & Input Text</option>
                                            <option value="iosFunctions">iOS Functions</option>
                                            <option value="ifElseSteps">If Else Steps</option>
                                            <option value="multiStep">Multi Step</option>
                                            <option value="repeatSteps">Repeat Steps</option>
                                            <option value="hookAction">Hook Action</option>
                                            <option value="takeScreenshot">Take Screenshot</option>
                                        </select>
                                    </div>

                                    <!-- Tap Action Form -->
                                    <div id="tapActionForm" class="action-form d-none">
                                        <ul class="nav nav-tabs mb-3" id="tapActionTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="tap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#tap-coordinates" type="button" role="tab" aria-controls="tap-coordinates" aria-selected="true">Coordinates</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="tap-image-tab" data-bs-toggle="tab" data-bs-target="#tap-image" type="button" role="tab" aria-controls="tap-image" aria-selected="false">Image</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="tap-locator-tab" data-bs-toggle="tab" data-bs-target="#tap-locator" type="button" role="tab" aria-controls="tap-locator" aria-selected="false">Locator</button>
                                            </li>
                                        </ul>
                                        <div class="tab-content" id="tapActionTabContent">
                                            <!-- Coordinates Tab -->
                                            <div class="tab-pane fade show active" id="tap-coordinates" role="tabpanel" aria-labelledby="tap-coordinates-tab">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label>X Coordinate</label>
                                                            <input type="number" id="tapX" class="form-control" value="0">
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label>Y Coordinate</label>
                                                            <input type="number" id="tapY" class="form-control" value="0">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="btn btn-outline-primary btn-sm mt-2" id="pickTapCoordinates">
                                                    <i class="bi bi-cursor"></i> Pick from Screen
                                                </button>
                                            </div>

                                            <!-- Image Tab -->
                                            <div class="tab-pane fade" id="tap-image" role="tabpanel" aria-labelledby="tap-image-tab">
                                                <div class="form-group">
                                                    <label for="tapImageFilename" class="form-label">Reference Image:</label>
                                                    <div class="input-group mb-2">
                                                        <select class="form-select" id="tapImageFilename">
                                                            <option value="">-- Select Image --</option>
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshTapImages">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                    
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="tapImageUseText">
                                                        <label class="form-check-label" for="tapImageUseText">
                                                            Enter image name manually
                                                        </label>
                                                    </div>
                                                    
                                                    <div class="input-group mb-2" id="tapImageTextInputGroup" style="display: none;">
                                                        <input type="text" class="form-control" id="tapImageTextInput" placeholder="Enter image name (e.g., button.png or env[image.png])">
                                                        <button class="btn btn-outline-secondary" type="button" id="validateTapImage">
                                                            Validate
                                                        </button>
                                                    </div>
                                                    <div class="alert alert-success mt-2 p-2 small" id="tapImageValidationSuccess" style="display: none;">
                                                        <i class="bi bi-check-circle-fill me-1"></i>
                                                        Image exists in reference images folder.
                                                    </div>
                                                    <div class="alert alert-danger mt-2 p-2 small" id="tapImageValidationError" style="display: none;">
                                                        <i class="bi bi-exclamation-triangle-fill me-1"></i>
                                                        <span id="tapImageValidationErrorMessage">Image not found.</span>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <button class="btn btn-outline-primary btn-sm" id="captureScreenImage" data-requires-connection="true" disabled>
                                                            <i class="bi bi-camera"></i> Capture from Screen
                                                        </button>
                                                        <div class="alert alert-info mt-2 p-2 small">
                                                            <i class="bi bi-info-circle-fill me-1"></i>
                                                            When capturing images, click and drag to select an area. Press ESC to cancel.
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <label for="tapThreshold" class="form-label">Similarity Threshold:</label>
                                                            <input type="number" class="form-control mb-2" id="tapThreshold" value="0.7" min="0" max="1" step="0.05">
                                                        </div>
                                                        <div class="col-6">
                                                            <label for="tapTimeout" class="form-label">Timeout (seconds):</label>
                                                            <input type="number" class="form-control mb-2" id="tapTimeout" value="20" min="1">
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">Uses Airtest to find and tap the reference image on screen.</small>
                                                </div>
                                            </div>

                                            <!-- Locator Tab -->
                                            <div class="tab-pane fade" id="tap-locator" role="tabpanel" aria-labelledby="tap-locator-tab">
                                                <!-- Primary Locator -->
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Primary Locator</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="form-group mb-3">
                                                            <label>Locator Type</label>
                                                            <select id="tapLocatorType" class="form-control">
                                                                <option value="id">ID</option>
                                                                <option value="xpath">XPath</option>
                                                                <option value="accessibility_id">Accessibility ID</option>
                                                                <option value="text">Text</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group mb-3">
                                                            <label>Locator Value</label>
                                                            <input type="text" id="tapLocatorValue" class="form-control" placeholder="Enter locator value">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Common Settings -->
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="tapLocatorTimeout" class="form-control" value="10" min="1" step="1">
                                                    <small class="text-muted">Total timeout will be divided among all locators</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Interval (seconds)</label>
                                                    <input type="number" id="tapLocatorInterval" class="form-control" value="0.5" min="0.1" step="0.1">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Fallback Action Section -->
                                        <div class="card mt-3">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Fallback Action</h6>
                                                <button type="button" class="btn btn-sm btn-primary" id="addTapFallbackBtn">
                                                    <i class="bi bi-plus"></i> Add Fallback Action
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="tapFallbackInfo" class="fallback-info" style="display: none;">
                                                    <div class="alert alert-success">
                                                        <i class="bi bi-check-circle-fill me-2"></i>
                                                        <span class="fallback-info-text">Fallback action added</span>
                                                    </div>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle-fill me-2"></i>
                                                    Add a fallback action that will be used if the primary tap action fails.
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>

                                    <!-- Double Tap Action Form -->
                                    <div id="doubleTapActionForm" class="action-form d-none">
                                        <ul class="nav nav-tabs mb-3" id="doubleTapActionTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="doubletap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#doubletap-coordinates" type="button" role="tab" aria-controls="doubletap-coordinates" aria-selected="true">Coordinates</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="doubletap-image-tab" data-bs-toggle="tab" data-bs-target="#doubletap-image" type="button" role="tab" aria-controls="doubletap-image" aria-selected="false">Image</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="doubletap-locator-tab" data-bs-toggle="tab" data-bs-target="#doubletap-locator" type="button" role="tab" aria-controls="doubletap-locator" aria-selected="false">Locator</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="doubletap-fallback-tab" data-bs-toggle="tab" data-bs-target="#doubletap-fallback" type="button" role="tab" aria-controls="doubletap-fallback" aria-selected="false">Fallback</button>
                                            </li>
                                        </ul>
                                        <div class="tab-content" id="doubleTapActionTabContent">
                                            <!-- Coordinates Tab -->
                                            <div class="tab-pane fade show active" id="doubletap-coordinates" role="tabpanel" aria-labelledby="doubletap-coordinates-tab">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label>X Coordinate</label>
                                                            <input type="number" id="doubleTapX" class="form-control" value="0">
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label>Y Coordinate</label>
                                                            <input type="number" id="doubleTapY" class="form-control" value="0">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="btn btn-outline-primary btn-sm mt-2" id="pickDoubleTapCoordinates">
                                                    <i class="bi bi-cursor"></i> Pick from Screen
                                                </button>
                                            </div>

                                            <!-- Image Tab -->
                                            <div class="tab-pane fade" id="doubletap-image" role="tabpanel" aria-labelledby="doubletap-image-tab">
                                                <div class="form-group">
                                                    <label for="doubleTapImageFilename" class="form-label">Reference Image:</label>
                                                    <div class="input-group mb-2">
                                                        <select class="form-select" id="doubleTapImageFilename">
                                                            <option value="">-- Select Image --</option>
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshDoubleTapImages">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                    
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="doubleTapImageUseText">
                                                        <label class="form-check-label" for="doubleTapImageUseText">
                                                            Enter image name manually
                                                        </label>
                                                    </div>
                                                    
                                                    <div class="input-group mb-2" id="doubleTapImageTextInputGroup" style="display: none;">
                                                        <input type="text" class="form-control" id="doubleTapImageTextInput" placeholder="Enter image name (e.g., button.png or env[image.png])">
                                                        <button class="btn btn-outline-secondary" type="button" id="validateDoubleTapImage">
                                                            Validate
                                                        </button>
                                                    </div>
                                                    <div class="alert alert-success mt-2 p-2 small" id="doubleTapImageValidationSuccess" style="display: none;">
                                                        <i class="bi bi-check-circle-fill me-1"></i>
                                                        Image exists in reference images folder.
                                                    </div>
                                                    <div class="alert alert-danger mt-2 p-2 small" id="doubleTapImageValidationError" style="display: none;">
                                                        <i class="bi bi-exclamation-triangle-fill me-1"></i>
                                                        <span id="doubleTapImageValidationErrorMessage">Image not found.</span>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <button class="btn btn-outline-primary btn-sm" id="captureDoubleTapScreenImage" data-requires-connection="true" disabled>
                                                            <i class="bi bi-camera"></i> Capture from Screen
                                                        </button>
                                                        <div class="alert alert-info mt-2 p-2 small">
                                                            <i class="bi bi-info-circle-fill me-1"></i>
                                                            When capturing images, click and drag to select an area. Press ESC to cancel.
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <label for="doubleTapThreshold" class="form-label">Similarity Threshold:</label>
                                                            <input type="number" class="form-control mb-2" id="doubleTapThreshold" value="0.7" min="0" max="1" step="0.05">
                                                        </div>
                                                        <div class="col-6">
                                                            <label for="doubleTapTimeout" class="form-label">Timeout (seconds):</label>
                                                            <input type="number" class="form-control mb-2" id="doubleTapTimeout" value="20" min="1">
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">Uses Airtest to find and double tap the reference image on screen.</small>
                                                </div>
                                            </div>

                                            <!-- Locator Tab -->
                                            <div class="tab-pane fade" id="doubletap-locator" role="tabpanel" aria-labelledby="doubletap-locator-tab">
                                                <!-- Primary Locator -->
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Primary Locator</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="form-group mb-3">
                                                            <label>Locator Type</label>
                                                            <select id="doubleTapLocatorType" class="form-control">
                                                                <option value="id">ID</option>
                                                                <option value="xpath">XPath</option>
                                                                <option value="accessibility_id">Accessibility ID</option>
                                                                <option value="text">Text</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group mb-3">
                                                            <label>Locator Value</label>
                                                            <input type="text" id="doubleTapLocatorValue" class="form-control" placeholder="Enter locator value">
                                                        </div>
                                                    </div>
                                                </div>



                                                <!-- Common Settings -->
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="doubleTapLocatorTimeout" class="form-control" value="10" min="1" step="1">
                                                    <small class="text-muted">Total timeout will be divided among all locators</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Interval (seconds)</label>
                                                    <input type="number" id="doubleTapLocatorInterval" class="form-control" value="0.5" min="0.1" step="0.1">
                                                </div>
                                            </div>

                                            <!-- Fallback Tab -->
                                            <div class="tab-pane fade" id="doubletap-fallback" role="tabpanel" aria-labelledby="doubletap-fallback-tab">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="card mb-3" id="doubleTapFallbackLocatorsSection">
                                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">Fallback Locators</h6>
                                                                <button type="button" class="btn btn-sm btn-primary" id="addDoubleTapFallbackLocator">
                                                                    <i class="bi bi-plus"></i> Add Fallback
                                                                </button>
                                                            </div>
                                                            <div class="card-body">
                                                                <div id="doubleTapFallbackLocatorsContainer" class="fallback-locators-container">
                                                                    <!-- Fallback locators will be added here dynamically -->
                                                                    <div class="alert alert-info">
                                                                        <i class="bi bi-info-circle-fill me-2"></i>
                                                                        Add fallback locators that will be tried in sequence if the primary locator fails.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="alert alert-primary">
                                                            <i class="bi bi-info-circle-fill me-2"></i>
                                                            Fallback locators can use different strategies (image, text, or element locators) and will be tried in the order listed if the primary locator fails.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Swipe Action Form -->
                                    <div id="swipeActionForm" class="action-form d-none">
                                        <div class="form-group mb-3">
                                            <label>Direction</label>
                                            <select id="swipeDirection" class="form-control">
                                                <option value="up">Up</option>
                                                <option value="down">Down</option>
                                                <option value="left">Left</option>
                                                <option value="right">Right</option>
                                                <option value="custom">Custom</option>
                                            </select>
                                        </div>

                                        <div id="swipeCustomSettings">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label>Start Point (% of screen)</label>
                                                    <div class="row g-2">
                                                        <div class="col-6">
                                                            <label for="swipeStartX" class="form-label small">X: <span id="swipeStartXValue">50</span>%</label>
                                                            <div class="d-flex gap-2">
                                                                <input type="range" id="swipeStartX" class="form-range" value="50" min="0" max="100" step="1"
                                                                   oninput="document.getElementById('swipeStartXValue').textContent = this.value; document.getElementById('swipeStartXInput').value = this.value;">
                                                                <input type="number" id="swipeStartXInput" class="form-control form-control-sm" value="50" min="0" max="100" style="width: 70px"
                                                                   oninput="document.getElementById('swipeStartX').value = this.value; document.getElementById('swipeStartXValue').textContent = this.value;">
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <label for="swipeStartY" class="form-label small">Y: <span id="swipeStartYValue">50</span>%</label>
                                                            <div class="d-flex gap-2">
                                                                <input type="range" id="swipeStartY" class="form-range" value="50" min="0" max="100" step="1"
                                                                   oninput="document.getElementById('swipeStartYValue').textContent = this.value; document.getElementById('swipeStartYInput').value = this.value;">
                                                                <input type="number" id="swipeStartYInput" class="form-control form-control-sm" value="50" min="0" max="100" style="width: 70px"
                                                                   oninput="document.getElementById('swipeStartY').value = this.value; document.getElementById('swipeStartYValue').textContent = this.value;">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label>End Point (% of screen)</label>
                                                    <div class="row g-2">
                                                        <div class="col-6">
                                                            <label for="swipeEndX" class="form-label small">X: <span id="swipeEndXValue">50</span>%</label>
                                                            <div class="d-flex gap-2">
                                                                <input type="range" id="swipeEndX" class="form-range" value="50" min="0" max="100" step="1"
                                                                   oninput="document.getElementById('swipeEndXValue').textContent = this.value; document.getElementById('swipeEndXInput').value = this.value;">
                                                                <input type="number" id="swipeEndXInput" class="form-control form-control-sm" value="50" min="0" max="100" style="width: 70px"
                                                                   oninput="document.getElementById('swipeEndX').value = this.value; document.getElementById('swipeEndXValue').textContent = this.value;">
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <label for="swipeEndY" class="form-label small">Y: <span id="swipeEndYValue">50</span>%</label>
                                                            <div class="d-flex gap-2">
                                                                <input type="range" id="swipeEndY" class="form-range" value="50" min="0" max="100" step="1"
                                                                   oninput="document.getElementById('swipeEndYValue').textContent = this.value; document.getElementById('swipeEndYInput').value = this.value;">
                                                                <input type="number" id="swipeEndYInput" class="form-control form-control-sm" value="50" min="0" max="100" style="width: 70px"
                                                                   oninput="document.getElementById('swipeEndY').value = this.value; document.getElementById('swipeEndYValue').textContent = this.value;">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <button class="btn btn-outline-primary btn-sm" id="pickSwipeCoordinates">
                                                    <i class="bi bi-cursor"></i> Draw Swipe Path on Screen
                                                </button>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Speed (ms)</label>
                                                    <input type="number" id="swipeDuration" class="form-control" value="300" min="50" max="5000" step="50">
                                                    <small class="text-muted">Duration of swipe in milliseconds</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Number of Swipes</label>
                                                    <input type="number" id="swipeCount" class="form-control" value="1" min="1" max="10" step="1">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Interval (sec)</label>
                                                    <input type="number" id="swipeInterval" class="form-control" value="0.5" min="0.1" max="10" step="0.1">
                                                    <small class="text-muted">Time between swipes</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Text Input Form -->
                                    <div id="textActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Text to Input</label>
                                                    <div class="input-group">
                                                        <input type="text" id="inputText" class="form-control" placeholder="Enter text to input">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Select data generator">
                                                            <i class="bi bi-magic"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" id="textGeneratorDropdown">
                                                            <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                                                            <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                                                            <!-- Generators will be populated by JS -->
                                                        </ul>
                                                    </div>
                                                    <div class="form-text mt-1" id="textGeneratorInfo">
                                                        <small class="text-muted">Select a data generator to automatically generate data during test execution</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tap and Type Form -->
                                    <div id="tapAndTypeActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <!-- Tabs for Locator vs Coordinates -->
                                                <ul class="nav nav-tabs mb-3" id="tapAndTypeMethodTabs" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="tapAndType-locator-tab" data-bs-toggle="tab" data-bs-target="#tapAndType-locator" type="button" role="tab" aria-controls="tapAndType-locator" aria-selected="true">Locator</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="tapAndType-coordinates-tab" data-bs-toggle="tab" data-bs-target="#tapAndType-coordinates" type="button" role="tab" aria-controls="tapAndType-coordinates" aria-selected="false">Coordinates</button>
                                                    </li>
                                                </ul>

                                                <!-- Tab Content -->
                                                <div class="tab-content" id="tapAndTypeMethodTabsContent">
                                                    <!-- Locator Tab -->
                                                    <div class="tab-pane fade show active" id="tapAndType-locator" role="tabpanel" aria-labelledby="tapAndType-locator-tab">
                                                        <div class="form-group mb-3">
                                                            <label>Locator Type</label>
                                                            <select id="tapAndTypeLocatorType" class="form-control">
                                                                <option value="id">ID</option>
                                                                <option value="xpath">XPath</option>
                                                                <option value="accessibility_id">Accessibility ID</option>
                                                                <option value="ios_predicate">iOS Predicate</option>
                                                                <option value="ios_class_chain">iOS Class Chain</option>
                                                                <option value="class_name">Class Name</option>
                                                                <option value="name">Name</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group mb-3">
                                                            <label>Locator Value</label>
                                                            <input type="text" id="tapAndTypeLocatorValue" class="form-control" placeholder="Enter locator value">
                                                            <small class="text-muted">For XCUIElementTypeOther elements, use XPath or iOS Class Chain</small>
                                                        </div>
                                                    </div>

                                                    <!-- Coordinates Tab -->
                                                    <div class="tab-pane fade" id="tapAndType-coordinates" role="tabpanel" aria-labelledby="tapAndType-coordinates-tab">
                                                        <div class="row">
                                                            <div class="col-6">
                                                                <div class="form-group mb-3">
                                                                    <label>X Coordinate</label>
                                                                    <input type="text" id="tapAndTypeX" class="form-control" value="0" placeholder="Enter coordinate or env[var_name]">
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="form-group mb-3">
                                                                    <label>Y Coordinate</label>
                                                                    <input type="text" id="tapAndTypeY" class="form-control" value="0" placeholder="Enter coordinate or env[var_name]">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group mb-3">
                                                            <button type="button" id="pickTapAndTypeCoordinates" class="btn btn-outline-primary">
                                                                <i class="bi bi-cursor"></i> Pick from Screen
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Common Fields for Both Methods -->
                                                <div class="form-group mb-3">
                                                    <label>Text to Input</label>
                                                    <div class="input-group">
                                                        <input type="text" id="tapAndTypeText" class="form-control" placeholder="Enter text to input">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Select data generator">
                                                            <i class="bi bi-magic"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" id="tapAndTypeGeneratorDropdown">
                                                            <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                                                            <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                                                            <!-- Generators will be populated by JS -->
                                                        </ul>
                                                    </div>
                                                    <div class="form-text mt-1" id="tapAndTypeGeneratorInfo">
                                                        <small class="text-muted">Select a data generator to automatically generate data during test execution</small>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="tapAndTypeTimeout" class="form-control" value="15" min="1" step="1">
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle-fill me-2"></i>
                                                    <strong>Tip:</strong> This action is specifically designed for iOS elements like XCUIElementTypeOther that don't accept standard text input methods. It first taps the element to focus it, then uses multiple iOS-specific methods to input text.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tap on Text Form -->
                                    <div id="tapOnTextActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Text to Find</label>
                                                    <input type="text" id="tapOnTextToFind" class="form-control" placeholder="Enter text to find on screen">
                                                    <small class="text-muted">The text to search for on the screen using OCR</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="tapOnTextTimeout" class="form-control" value="30" min="1" step="1">
                                                    <small class="text-muted">Maximum time to wait for the text to appear (default: 30s)</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <div class="form-check">
                                                        <input type="checkbox" id="tapOnTextDoubleTap" class="form-check-input">
                                                        <label class="form-check-label" for="tapOnTextDoubleTap">Double Tap</label>
                                                    </div>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle-fill me-2"></i>
                                                    <strong>Tip:</strong> This action uses OCR (Optical Character Recognition) to find text on the screen and tap it. It works best with clear, high-contrast text.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Send Keys Form -->
                                    <div id="sendKeysActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="sendKeysLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class_name">Class Name</option>
                                                        <option value="name">Name</option>
                                                        <option value="css">CSS Selector</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="sendKeysLocatorValue" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Text to Send</label>
                                                    <div class="input-group">
                                                        <input type="text" id="sendKeysText" class="form-control" placeholder="Enter text to send">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Select data generator">
                                                            <i class="bi bi-magic"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" id="sendKeysGeneratorDropdown">
                                                            <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                                                            <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                                                            <!-- Generators will be populated by JS -->
                                                        </ul>
                                                    </div>
                                                    <div class="form-text mt-1" id="sendKeysGeneratorInfo">
                                                        <small class="text-muted">Select a data generator to automatically generate data during test execution</small>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <div class="form-check">
                                                        <input type="checkbox" id="sendKeysClearFirst" class="form-check-input" checked>
                                                        <label class="form-check-label" for="sendKeysClearFirst">Clear field first</label>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="sendKeysTimeout" class="form-control" value="15" min="1" step="1">
                                                    <small class="text-muted">Maximum time to wait for element (default: 15s)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Input Text to Element Form -->
                                    <div id="inputTextActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="inputTextLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class_name">Class Name</option>
                                                        <option value="name">Name</option>
                                                        <option value="css">CSS Selector</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="inputTextLocatorValue" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Text to Input</label>
                                                    <div class="input-group">
                                                        <input type="text" id="inputTextValue" class="form-control" placeholder="Enter text to input">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Select data generator">
                                                            <i class="bi bi-magic"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" id="inputTextGeneratorDropdown">
                                                            <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                                                            <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                                                            <!-- Generators will be populated by JS -->
                                                        </ul>
                                                    </div>
                                                    <div class="form-text mt-1" id="inputTextGeneratorInfo">
                                                        <small class="text-muted">Select a data generator to automatically generate data during test execution</small>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <div class="form-check">
                                                        <input type="checkbox" id="inputTextClearFirst" class="form-check-input" checked>
                                                        <label class="form-check-label" for="inputTextClearFirst">Clear field first</label>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="inputTextTimeout" class="form-control" value="20" min="1" step="1">
                                                    <small class="text-muted">Maximum time to wait for element (default: 20s)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Key Press Form -->
                                    <div id="keyActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Key to Press</label>
                                                    <select id="keyCode" class="form-control">
                                                        <option value="4">Back</option>
                                                        <option value="3">Home</option>
                                                        <option value="82">Menu</option>
                                                        <option value="19">Up</option>
                                                        <option value="20">Down</option>
                                                        <option value="21">Left</option>
                                                        <option value="22">Right</option>
                                                        <option value="66">Enter</option>
                                                        <option value="67">Backspace</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Wait Form -->
                                    <div id="waitActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Wait time (seconds)</label>
                                                    <input type="number" id="waitTime" class="form-control" value="1" min="0.1" step="0.1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Launch App Form -->
                                    <div id="launchAppActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Package Name</label>
                                                    <input type="text" id="appPackage" class="form-control" placeholder="com.example.app">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Terminate App Form -->
                                    <div id="terminateAppActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Package Name</label>
                                                    <input type="text" id="terminatePackage" class="form-control" placeholder="com.example.app">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Restart App Form -->
                                    <div id="restartAppActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Package Name</label>
                                                    <input type="text" id="restartPackage" class="form-control" placeholder="com.example.app">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Uninstall App Form -->
                                    <div id="uninstallAppActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Package Name / Bundle ID</label>
                                                    <input type="text" id="uninstallPackage" class="form-control" placeholder="com.example.app">
                                                    <small class="text-muted">Enter the package name (Android) or bundle ID (iOS) of the app to uninstall</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Wait Till Image Form -->
                                    <div id="waitTillActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="waitTillLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="text">Text</option>
                                                        <option value="image">Image</option>
                                                        <option value="position">Position</option>
                                                    </select>
                                                </div>
                                                <div id="waitTillImageSelect" class="form-group mb-3">
                                                    <label>Reference Image</label>
                                                    <div class="input-group mb-2">
                                                        <select class="form-select" id="waitTillImage">
                                                            <option value="">-- Select Image --</option>
                                                            <!-- Will be populated dynamically -->
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshWaitTillImages">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                    <div class="mb-3">
                                                        <button class="btn btn-outline-primary btn-sm" id="captureWaitTillScreenImage" data-requires-connection="true" disabled>
                                                            <i class="bi bi-camera"></i> Capture from Screen
                                                        </button>
                                                        <div class="alert alert-info mt-2 p-2 small">
                                                            <i class="bi bi-info-circle-fill me-1"></i>
                                                            When capturing images, click and drag to select an area. Press ESC to cancel.
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label for="waitTillThreshold">Match Threshold (0-1)</label>
                                                            <input type="number" id="waitTillThreshold" class="form-control" min="0.1" max="1.0" step="0.05" value="0.7">
                                                            <small class="form-text text-muted">Lower values are more lenient, higher values require closer matches.</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="waitTillLocatorValue" class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="waitTillLocator" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="waitTillTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Interval (seconds)</label>
                                                    <input type="number" id="waitTillInterval" class="form-control" value="0.5" min="0.1" step="0.1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Click Element Form -->
                                    <div id="clickElementActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="clickElementLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class">Class Name</option>
                                                        <option value="name">Name</option>
                                                        <option value="text">Text</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="clickElementLocator" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="clickElementTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Exists Form -->
                                    <div id="existsActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="existsLocatorType">Locator Type</label>
                                                    <select id="existsLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class">Class Name</option>
                                                        <option value="name">Name</option>
                                                        <option value="text">Text</option>
                                                        <option value="image">Image</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12" id="existsLocatorValueContainer">
                                                <div class="form-group mb-3">
                                                    <label for="existsLocatorValue">Locator Value</label>
                                                    <input type="text" id="existsLocatorValue" class="form-control" placeholder="Enter the locator value">
                                                </div>
                                            </div>
                                            <div class="col-12" id="existsImageContainer" style="display:none;">
                                                <div class="form-group mb-3">
                                                    <label for="existsImage">Reference Image</label>
                                                    <select id="existsImage" class="form-control">
                                                        <!-- Will be populated dynamically -->
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="existsTimeout">Timeout (seconds)</label>
                                                    <input type="number" id="existsTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Double Click Form -->
                                    <div id="doubleClickActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label>X Coordinate</label>
                                                    <input type="number" id="doubleClickX" class="form-control" value="0">
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label>Y Coordinate</label>
                                                    <input type="number" id="doubleClickY" class="form-control" value="0">
                                                </div>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm mt-2" id="pickDoubleClickCoordinates">
                                            <i class="bi bi-cursor"></i> Pick from Screen
                                        </button>
                                    </div>

                                    <!-- Input & Clear Text Form -->
                                    <div id="textClearActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label>Text to Input (field will be cleared first)</label>
                                                    <div class="input-group">
                                                        <input type="text" id="textClearInput" class="form-control" placeholder="Field will be cleared first, then this text will be entered">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Select data generator">
                                                            <i class="bi bi-magic"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" id="textClearGeneratorDropdown">
                                                            <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                                                            <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                                                            <!-- Generators will be populated by JS -->
                                                        </ul>
                                                    </div>
                                                    <div class="form-text mt-1" id="textClearGeneratorInfo">
                                                        <small class="text-muted">Select a data generator to automatically generate data during test execution</small>
                                                    </div>
                                                </div>
                                                <div class="form-group mt-2">
                                                    <label>Delay Before Input (ms)</label>
                                                    <input type="number" id="textClearDelay" class="form-control" value="500" min="100" step="100">
                                                    <small class="text-muted">Delay in milliseconds between clearing and inputting text</small>
                                                </div>

                                                <!-- Skip Condition Section -->
                                                <div class="form-group mt-3">
                                                    <div class="form-check">
                                                        <input type="checkbox" id="textClearSkipCondition" class="form-check-input">
                                                        <label class="form-check-label" for="textClearSkipCondition">Skip this step if element exists</label>
                                                    </div>
                                                </div>

                                                <!-- Skip Condition Options (shown when checkbox is checked) -->
                                                <div id="textClearSkipConditionOptions" class="mt-3" style="display: none;">
                                                    <ul class="nav nav-tabs mb-3" id="textClearSkipTabs" role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" id="textClear-skip-locator-tab" data-bs-toggle="tab" data-bs-target="#textClear-skip-locator" type="button" role="tab" aria-controls="textClear-skip-locator" aria-selected="true">Locator</button>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="textClear-skip-image-tab" data-bs-toggle="tab" data-bs-target="#textClear-skip-image" type="button" role="tab" aria-controls="textClear-skip-image" aria-selected="false">Image</button>
                                                        </li>
                                                    </ul>

                                                    <div class="tab-content" id="textClearSkipTabContent">
                                                        <!-- Locator Tab -->
                                                        <div class="tab-pane fade show active" id="textClear-skip-locator" role="tabpanel" aria-labelledby="textClear-skip-locator-tab">
                                                            <div class="form-group mb-3">
                                                                <label>Locator Type</label>
                                                                <select id="textClearSkipLocatorType" class="form-control">
                                                                    <option value="id">ID</option>
                                                                    <option value="xpath">XPath</option>
                                                                    <option value="accessibility_id">Accessibility ID</option>
                                                                    <option value="class_name">Class Name</option>
                                                                    <option value="name">Name</option>
                                                                </select>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label>Locator Value</label>
                                                                <input type="text" id="textClearSkipLocatorValue" class="form-control" placeholder="Enter locator value">
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label>Timeout (seconds)</label>
                                                                <input type="number" id="textClearSkipLocatorTimeout" class="form-control" value="5" min="1" step="1">
                                                                <small class="text-muted">Maximum time to check for element (default: 5s)</small>
                                                            </div>
                                                        </div>

                                                        <!-- Image Tab -->
                                                        <div class="tab-pane fade" id="textClear-skip-image" role="tabpanel" aria-labelledby="textClear-skip-image-tab">
                                                            <div class="form-group mb-3">
                                                                <label>Reference Image</label>
                                                                <div class="input-group">
                                                                    <select id="textClearSkipImage" class="form-control">
                                                                        <option value="">-- Select Image --</option>
                                                                        <!-- Will be populated dynamically -->
                                                                    </select>
                                                                    <button class="btn btn-outline-secondary" type="button" id="refreshTextClearSkipImages">
                                                                        <i class="bi bi-arrow-clockwise"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label>Similarity Threshold</label>
                                                                <input type="number" id="textClearSkipImageThreshold" class="form-control" value="0.7" min="0.1" max="1.0" step="0.05">
                                                                <small class="text-muted">Lower values are less strict (0.1-1.0)</small>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label>Timeout (seconds)</label>
                                                                <input type="number" id="textClearSkipImageTimeout" class="form-control" value="5" min="1" step="1">
                                                                <small class="text-muted">Maximum time to check for image (default: 5s)</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Click Image (OCR) Form -->
                                    <div id="clickImageActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="ocrTextToFind">Text to Find & Click</label>
                                                    <input type="text" id="ocrTextToFind" class="form-control" placeholder="Enter exact text visible on screen">
                                                    <small class="text-muted">Uses OCR to find this text and click its center.</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="ocrTimeout">Timeout (seconds)</label>
                                                    <input type="number" id="ocrTimeout" class="form-control" value="20" min="1" step="1">
                                                    <small class="text-muted">How long to search for the text (default: 20s).</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Click Image (Airtest) Form -->
                                    <div id="clickImageAirtestActionForm" class="action-form d-none">
                                        <label for="airtestImageFilename" class="form-label">Reference Image:</label>
                                        <div class="input-group mb-2">
                                            <select class="form-select" id="airtestImageFilename">
                                                <option value="" selected disabled>Select an image...</option>
                                                <!-- Options populated by JS -->
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refreshClickImageAirtestImages">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                        <div class="mb-3">
                                            <button class="btn btn-outline-primary btn-sm" id="captureClickImageAirtestScreenImage" data-requires-connection="true" disabled>
                                                <i class="bi bi-camera"></i> Capture from Screen
                                            </button>
                                        </div>
                                        <label for="airtestThreshold" class="form-label">Similarity Threshold:</label>
                                        <input type="number" class="form-control mb-2" id="airtestThreshold" value="0.7" min="0" max="1" step="0.05">
                                        <label for="airtestTimeout" class="form-label">Timeout (seconds):</label>
                                        <input type="number" class="form-control mb-2" id="airtestTimeout" value="20" min="1">
                                    </div>

                                    <!-- New Double Click Image Form -->
                                    <div id="doubleClickImageActionForm" class="action-form d-none">
                                        <label for="doubleClickImagePath" class="form-label">Reference Image:</label>
                                        <select class="form-select mb-2" id="doubleClickImagePath">
                                            <option value="" selected disabled>Select an image...</option>
                                            <!-- Options populated by JS -->
                                        </select>
                                        <label for="doubleClickImageThreshold" class="form-label">Similarity Threshold:</label>
                                        <input type="number" class="form-control mb-2" id="doubleClickImageThreshold" value="0.7" min="0" max="1" step="0.05">
                                        <label for="doubleClickImageTimeout" class="form-label">Timeout (seconds):</label>
                                        <input type="number" class="form-control mb-2" id="doubleClickImageTimeout" value="20" min="1">
                                        <small class="text-muted">Uses AirTest to find and double-click the reference image on screen.</small>
                                    </div>

                                    <!-- Wait for Image (Airtest) Form -->
                                    <div id="waitImageAirtestActionForm" class="action-form d-none">
                                        <label for="waitAirtestImageFilename" class="form-label">Reference Image:</label>
                                        <div class="input-group mb-2">
                                            <select class="form-select" id="waitAirtestImageFilename">
                                                <option value="" selected disabled>Select an image...</option>
                                                <!-- Options populated by JS -->
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refreshWaitImageAirtestImages">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                        <div class="mb-3">
                                            <button class="btn btn-outline-primary btn-sm" id="captureWaitImageAirtestScreenImage" data-requires-connection="true" disabled>
                                                <i class="bi bi-camera"></i> Capture from Screen
                                            </button>
                                        </div>
                                        <label for="waitAirtestThreshold" class="form-label">Similarity Threshold:</label>
                                        <input type="number" class="form-control mb-2" id="waitAirtestThreshold" value="0.7" min="0" max="1" step="0.05">
                                        <label for="waitAirtestTimeout" class="form-label">Timeout (seconds):</label>
                                        <input type="number" class="form-control mb-2" id="waitAirtestTimeout" value="30" min="1">
                                    </div>

                                    <!-- Double Click Image (Airtest) Form -->
                                    <div id="doubleClickImageAirtestActionForm" class="action-form d-none">
                                        <label for="doubleClickAirtestImageFilename" class="form-label">Reference Image:</label>
                                        <div class="input-group mb-2">
                                            <select class="form-select" id="doubleClickAirtestImageFilename">
                                                <option value="" selected disabled>Select an image...</option>
                                                <!-- Options populated by JS -->
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refreshDoubleClickImageAirtestImages">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                        <div class="mb-3">
                                            <button class="btn btn-outline-primary btn-sm" id="captureDoubleClickImageAirtestScreenImage" data-requires-connection="true" disabled>
                                                <i class="bi bi-camera"></i> Capture from Screen
                                            </button>
                                        </div>
                                        <label for="doubleClickAirtestThreshold" class="form-label">Similarity Threshold:</label>
                                        <input type="number" class="form-control mb-2" id="doubleClickAirtestThreshold" value="0.7" min="0" max="1" step="0.05">
                                        <label for="doubleClickAirtestTimeout" class="form-label">Timeout (seconds):</label>
                                        <input type="number" class="form-control mb-2" id="doubleClickAirtestTimeout" value="20" min="1">
                                    </div>

                                    <!-- Swipe Till Visible Action Form -->
                                    <div id="swipeTillVisibleActionForm" class="action-form d-none">
                                        <h4>Swipe Till Visible</h4>
                                        <div class="form-group mb-3">
                                            <label>Direction</label>
                                            <select id="swipeTillVisibleDirection" class="form-control">
                                                <option value="up">Up</option>
                                                <option value="down">Down</option>
                                                <option value="left">Left</option>
                                                <option value="right">Right</option>
                                                <option value="custom">Custom</option>
                                            </select>
                                        </div>

                                        <div id="swipeTillVisibleCustomSettings">
                                            <div class="form-row">
                                                <div class="col-md-6 form-group mb-3">
                                                    <label>Start X (%)</label>
                                                    <input type="number" id="swipeTillVisibleStartX" class="form-control" value="50" min="0" max="100">
                                                </div>
                                                <div class="col-md-6 form-group mb-3">
                                                    <label>Start Y (%)</label>
                                                    <input type="number" id="swipeTillVisibleStartY" class="form-control" value="70" min="0" max="100">
                                                </div>
                                            </div>
                                            <div class="form-row">
                                                <div class="col-md-6 form-group mb-3">
                                                    <label>End X (%)</label>
                                                    <input type="number" id="swipeTillVisibleEndX" class="form-control" value="50" min="0" max="100">
                                                </div>
                                                <div class="col-md-6 form-group mb-3">
                                                    <label>End Y (%)</label>
                                                    <input type="number" id="swipeTillVisibleEndY" class="form-control" value="30" min="0" max="100">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label>Duration (ms)</label>
                                            <input type="number" id="swipeTillVisibleDuration" class="form-control" value="300" min="100">
                                        </div>
                                        <div class="form-group mb-3">
                                            <label>Count</label>
                                            <input type="number" id="swipeTillVisibleCount" class="form-control" value="1" min="1">
                                        </div>
                                        <div class="form-group mb-3">
                                            <label>Interval (seconds)</label>
                                            <input type="number" id="swipeTillVisibleInterval" class="form-control" value="0.5" min="0.1" step="0.1">
                                        </div>

                                        <div class="form-group mb-3">
                                            <label>Locator Type</label>
                                            <select id="swipeTillVisibleLocatorType" class="form-control">
                                                <option value="id">ID</option>
                                                <option value="xpath">Xpath</option>
                                                <option value="accessibilityid">AccessibilityID</option>
                                                <option value="text">Text</option>
                                            </select>
                                        </div>

                                        <div id="swipeTillVisibleLocatorValueDiv" class="form-group mb-3">
                                            <label>Locator Value</label>
                                            <input type="text" id="swipeTillVisibleLocatorValue" class="form-control" placeholder="Enter locator value">
                                        </div>

                                        <div id="swipeTillVisibleTextDiv" class="form-group mb-3" style="display:none;">
                                            <label>Text to Find</label>
                                            <input type="text" id="swipeTillVisibleTextToFind" class="form-control" placeholder="Enter text to find on screen">
                                            <small class="text-muted">Uses OCR to find this text on the screen.</small>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label>Reference Image (Optional)</label>
                                            <select id="swipeTillVisibleReferenceImage" class="form-control">
                                                <option value="">-- No Image --</option>
                                            </select>
                                            <button type="button" id="refreshSwipeTillVisibleImages" class="btn btn-secondary btn-sm mt-2">Refresh Images</button>
                                            <small class="text-muted">If selected, this image will be used instead of the locator.</small>

                                            <div class="mt-3">
                                                <label for="swipeTillVisibleThreshold" class="form-label">Similarity Threshold:</label>
                                                <input type="number" class="form-control mb-2" id="swipeTillVisibleThreshold" value="0.7" min="0" max="1" step="0.05">
                                                <label for="swipeTillVisibleImageTimeout" class="form-label">Timeout (seconds):</label>
                                                <input type="number" class="form-control mb-2" id="swipeTillVisibleImageTimeout" value="20" min="1">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Check If Exists Form -->
                                    <div id="existsActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="existsLocatorType">Locator Type</label>
                                                    <select id="existsLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class">Class Name</option>
                                                        <option value="name">Name</option>
                                                        <option value="text">Text</option>
                                                        <option value="image">Image</option>
                                                    </select>
                                                </div>
                                                <div id="existsLocatorValueContainer" class="form-group mb-3">
                                                    <label for="existsLocatorValue">Locator Value</label>
                                                    <input type="text" id="existsLocatorValue" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div id="existsImageContainer" class="form-group mb-3" style="display:none;">
                                                    <label>Reference Image</label>
                                                    <select id="existsImage" class="form-control">
                                                        <!-- Will be populated dynamically -->
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="existsTimeout">Timeout (seconds)</label>
                                                    <input type="number" id="existsTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tap If Image Exists Action Form -->
                                    <div id="tapIfImageExistsActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Reference Image</label>
                                                    <div class="input-group mb-2">
                                                        <select class="form-select" id="tapIfImageExistsFilename">
                                                            <option value="" selected disabled>Select an image...</option>
                                                            <!-- Options populated by JS -->
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshTapIfImageExistsImages">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="tapIfImageExistsUseEnvVar">
                                                        <label class="form-check-label" for="tapIfImageExistsUseEnvVar">
                                                            Use Environment Variable
                                                        </label>
                                                    </div>
                                                    <div id="tapIfImageExistsEnvVarContainer" class="mb-2 d-none">
                                                        <input type="text" id="tapIfImageExistsEnvVar" class="form-control" placeholder="Enter environment variable name (e.g., IMAGE_FILE)">
                                                        <small class="text-muted">The variable should contain the image filename</small>
                                                    </div>
                                                    <div class="mb-3">
                                                        <button class="btn btn-outline-primary btn-sm" id="captureTapIfImageExistsScreenImage" data-requires-connection="true" disabled>
                                                            <i class="bi bi-camera"></i> Capture from Screen
                                                        </button>
                                                        <div class="alert alert-info mt-2 p-2 small">
                                                            <i class="bi bi-info-circle-fill me-1"></i>
                                                            When capturing images, click and drag to select an area. Press ESC to cancel.
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Similarity Threshold</label>
                                                    <input type="number" id="tapIfImageExistsThreshold" class="form-control" value="0.7" min="0" max="1" step="0.05">
                                                    <small class="form-text text-muted">Lower values are less strict (0.1-1.0)</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="tapIfImageExistsTimeout" class="form-control" value="5" min="1" step="1">
                                                    <small class="form-text text-muted">Maximum time to wait for the image to appear</small>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> This action will tap on the image if it exists on the screen, and do nothing if it doesn't.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hide Keyboard Action Form -->
                                    <div id="hideKeyboardActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> This action will hide the keyboard on the device. No additional parameters are required.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tap on Text Action Form -->
                                    <div id="tapOnTextActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="tapOnTextToFind">Text to Find & Tap</label>
                                                    <input type="text" id="tapOnTextToFind" class="form-control" placeholder="Enter exact text visible on screen">
                                                    <small class="text-muted">Uses OCR to find this text and tap its center.</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="tapOnTextTimeout">Timeout (seconds)</label>
                                                    <input type="number" id="tapOnTextTimeout" class="form-control" value="30" min="1" step="1">
                                                    <small class="text-muted">How long to search for the text (default: 30s).</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="tapOnTextDoubleTap">
                                                        <label class="form-check-label" for="tapOnTextDoubleTap">
                                                            Double Tap
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">Check to perform a double tap instead of a single tap.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Log Action Form -->
                                    <div id="addLogActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="addLogMessage">Log Message</label>
                                                    <input type="text" id="addLogMessage" class="form-control" placeholder="Enter log message">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="addLogTakeScreenshot" checked>
                                                        <label class="form-check-label" for="addLogTakeScreenshot">
                                                            Take Screenshot
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> This action will add a log entry with an optional screenshot. It always passes.
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- Airplane Mode Action Form -->
                                    <div id="airplaneModeActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="airplaneModeEnabled" value="true">
                                                        <label class="form-check-label" for="airplaneModeEnabled">
                                                            Enable Airplane Mode
                                                        </label>
                                                    </div>
                                                    <small class="form-text text-muted">Check to enable airplane mode, uncheck to disable it.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Media Action Form -->
                                    <div id="addMediaActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label for="mediaFile">Select Media File</label>
                                                    <input type="file" class="form-control" id="mediaFile" accept="image/*,video/*">
                                                    <small class="form-text text-muted">Select a media file to push to the device. File will be saved in the "files_to_push" directory.</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="mediaDestination">Destination Path (Optional)</label>
                                                    <input type="text" class="form-control" id="mediaDestination" placeholder="Leave blank for default location">
                                                    <small class="form-text text-muted">For Android: /sdcard/Pictures/filename.jpg, For iOS: photos</small>
                                                </div>
                                                <div class="form-group">
                                                    <button class="btn btn-primary" type="button" id="uploadMediaBtn">
                                                        <i class="bi bi-upload"></i> Upload File
                                                    </button>
                                                </div>
                                                <div id="uploadStatus" class="mt-2 d-none">
                                                    <div class="progress mb-2">
                                                        <div id="uploadProgressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                                    </div>
                                                    <div id="uploadMessage"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Device Back Action Form -->
                                    <div id="deviceBackActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-warning">
                                                    <i class="bi bi-exclamation-triangle"></i> This action will press the device's back button. It is only supported on Android devices.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- iOS Functions Action Form -->
                                    <div id="iosFunctionsActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle"></i> These functions are only available for iOS devices using Airtest.
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="iosFunction" class="form-label">iOS Function</label>
                                                    <select id="iosFunction" name="iosFunction" class="form-select">
                                                        <option value="home">Press Home Button</option>
                                                        <option value="lock">Lock Device</option>
                                                        <option value="unlock">Unlock Device</option>
                                                        <option value="press">Press Key</option>
                                                        <option value="alert_accept">Accept Alert</option>
                                                        <option value="alert_dismiss">Dismiss Alert</option>
                                                        <option value="alert_click">Click Alert Button</option>
                                                        <option value="alert_wait">Wait for Alert</option>
                                                        <option value="alert_buttons">Get Alert Buttons</option>
                                                        <option value="alert_exists">Check if Alert Exists</option>
                                                        <option value="get_clipboard">Get Clipboard Content</option>
                                                        <option value="set_clipboard">Set Clipboard Content</option>
                                                        <option value="paste">Paste Clipboard Content</option>
                                                        <option value="text">Input Text</option>
                                                        <option value="get_ip_address">Get Device IP Address</option>
                                                        <option value="device_status">Get Device Status</option>
                                                        <option value="is_locked">Check if Device is Locked</option>
                                                        <option value="push">Push File to Device</option>
                                                        <option value="clear_app">Clear App Data</option>
                                                    </select>
                                                </div>

                                                <!-- Container for dynamically generated function parameters -->
                                                <div id="iosFunctionParamsContainer" class="mt-3">
                                                    <!-- Parameters will be dynamically inserted here by JS -->
                                                </div>

                                                <!-- Legacy parameter containers that can be removed after testing -->
                                                <div id="iosSetClipboardParams" class="d-none mt-3">
                                                    <label for="clipboardContent" class="form-label">Content</label>
                                                    <input type="text" id="clipboardContent" name="clipboardContent" class="form-control" placeholder="Text to set in clipboard">
                                                </div>

                                                <div id="iosPressKeyParams" class="d-none mt-3">
                                                    <label for="iosKeyName" class="form-label">Key Name</label>
                                                    <input type="text" id="iosKeyName" name="iosKeyName" class="form-control" placeholder="e.g., home, volumeUp">
                                                </div>

                                                <div id="iosAlertClickParams" class="d-none mt-3">
                                                    <label for="iosAlertButton" class="form-label">Button Name</label>
                                                    <input type="text" id="iosAlertButton" name="iosAlertButton" class="form-control" placeholder="e.g., OK, Cancel, Allow">
                                                </div>

                                                <div id="iosAlertWaitParams" class="d-none mt-3">
                                                    <label for="iosAlertTimeout" class="form-label">Timeout (seconds)</label>
                                                    <input type="number" id="iosAlertTimeout" name="iosAlertTimeout" class="form-control" value="10" min="1" max="60">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Get Value Action Form -->
                                    <div id="getValueActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="getValueLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class">Class Name</option>
                                                        <option value="name">Name</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="getValueLocatorValue" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Attribute</label>
                                                    <select id="getValueAttribute" class="form-control">
                                                        <option value="text">text</option>
                                                        <option value="content-desc">content-desc</option>
                                                        <option value="value">value</option>
                                                        <option value="label">label</option>
                                                        <option value="name">name</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="getValueTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Compare Value Action Form -->
                                    <div id="compareValueActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label>Locator Type</label>
                                                    <select id="compareValueLocatorType" class="form-control">
                                                        <option value="id">ID</option>
                                                        <option value="xpath">XPath</option>
                                                        <option value="accessibility_id">Accessibility ID</option>
                                                        <option value="class">Class Name</option>
                                                        <option value="name">Name</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Locator Value</label>
                                                    <input type="text" id="compareValueLocatorValue" class="form-control" placeholder="Enter locator value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Attribute</label>
                                                    <select id="compareValueAttribute" class="form-control">
                                                        <option value="text">text</option>
                                                        <option value="content-desc">content-desc</option>
                                                        <option value="value">value</option>
                                                        <option value="label">label</option>
                                                        <option value="name">name</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Expected Value</label>
                                                    <input type="text" id="compareValueExpected" class="form-control" placeholder="Enter expected value">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label>Timeout (seconds)</label>
                                                    <input type="number" id="compareValueTimeout" class="form-control" value="10" min="1" step="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Get Parameter Action Form -->
                                    <div id="getParamActionForm" class="action-form d-none">
                                        <div class="form-group mb-3">
                                            <label for="getParamName">Parameter Name</label>
                                            <input type="text" id="getParamName" class="form-control" placeholder="Enter parameter name">
                                            <small class="text-muted">Enter the name of the parameter to retrieve from Global Values.</small>
                                        </div>
                                    </div>

                                    <!-- Set Parameter Action Form -->
                                    <div id="setParamActionForm" class="action-form d-none">
                                        <div class="form-group mb-3">
                                            <label for="setParamName">Parameter Name</label>
                                            <input type="text" id="setParamName" class="form-control" placeholder="Enter parameter name">
                                        </div>
                                        <div class="form-group mb-3">
                                            <label for="setParamValue">Parameter Value</label>
                                            <input type="text" id="setParamValue" class="form-control" placeholder="Enter parameter value">
                                        </div>
                                        <small class="text-muted">
                                            If the parameter exists in Global Values, it will be updated. Otherwise, a new parameter will be created.
                                            <br>Values will be automatically converted to the appropriate type when possible (numbers, booleans, etc.).
                                        </small>
                                    </div>

                                    <!-- If Else Steps Action Form -->
                                    <div id="ifElseStepsActionForm" class="action-form d-none">
                                        <div class="card mb-3">
                                            <div class="card-header bg-light">
                                                If Condition
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group mb-3">
                                                    <label for="ifConditionType">Condition Type</label>
                                                    <select id="ifConditionType" class="form-control">
                                                        <option value="exists">Check If Exists</option>
                                                        <option value="not_exists">Check If Not Exists</option>
                                                        <option value="visible">Check If Visible</option>
                                                        <option value="contains_text">Check If Contains Text</option>
                                                        <option value="value_equals">Check If Value Equals</option>
                                                        <option value="value_contains">Check If Value Contains</option>
                                                        <option value="has_attribute">Check If Element Has Attribute</option>
                                                        <option value="screen_contains">Check If Screen Contains Image</option>
                                                    </select>
                                                </div>

                                                <!-- Exists condition form fields -->
                                                <div id="ifExistsConditionForm">
                                                    <div class="form-group mb-3">
                                                        <label for="ifExistsLocatorType">Locator Type</label>
                                                        <select id="ifExistsLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                            <option value="text">Text</option>
                                                            <option value="image">Image</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3" id="ifExistsLocatorValueContainer">
                                                        <label for="ifExistsLocatorValue">Locator Value</label>
                                                        <input type="text" id="ifExistsLocatorValue" class="form-control" placeholder="Enter the locator value">
                                                    </div>
                                                    <div class="form-group mb-3" id="ifExistsImageContainer" style="display:none;">
                                                        <label>Reference Image</label>
                                                        <select id="ifExistsImage" class="form-control">
                                                            <!-- Will be populated dynamically -->
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifExistsTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifExistsTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Visible condition form fields -->
                                                <div id="ifVisibleConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifVisibleLocatorType">Locator Type</label>
                                                        <select id="ifVisibleLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                            <option value="text">Text</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifVisibleLocatorValue">Locator Value</label>
                                                        <input type="text" id="ifVisibleLocatorValue" class="form-control" placeholder="Enter the locator value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifVisibleTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifVisibleTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Contains Text condition form fields -->
                                                <div id="ifContainsTextConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifContainsTextLocatorType">Element Locator Type</label>
                                                        <select id="ifContainsTextLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifContainsTextLocatorValue">Element Locator Value</label>
                                                        <input type="text" id="ifContainsTextLocatorValue" class="form-control" placeholder="Enter the element locator value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifContainsTextValue">Text to Check For</label>
                                                        <input type="text" id="ifContainsTextValue" class="form-control" placeholder="Enter text to check for">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifContainsTextTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifContainsTextTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Value Equals condition form fields -->
                                                <div id="ifValueEqualsConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueEqualsLocatorType">Element Locator Type</label>
                                                        <select id="ifValueEqualsLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueEqualsLocatorValue">Element Locator Value</label>
                                                        <input type="text" id="ifValueEqualsLocatorValue" class="form-control" placeholder="Enter the element locator value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueEqualsExpectedValue">Expected Value</label>
                                                        <input type="text" id="ifValueEqualsExpectedValue" class="form-control" placeholder="Enter expected value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueEqualsTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifValueEqualsTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Value Contains condition form fields -->
                                                <div id="ifValueContainsConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueContainsLocatorType">Element Locator Type</label>
                                                        <select id="ifValueContainsLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueContainsLocatorValue">Element Locator Value</label>
                                                        <input type="text" id="ifValueContainsLocatorValue" class="form-control" placeholder="Enter the element locator value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueContainsExpectedValue">Text to Check For</label>
                                                        <input type="text" id="ifValueContainsExpectedValue" class="form-control" placeholder="Enter text to check for">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifValueContainsTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifValueContainsTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Has Attribute condition form fields -->
                                                <div id="ifHasAttributeConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifHasAttributeLocatorType">Element Locator Type</label>
                                                        <select id="ifHasAttributeLocatorType" class="form-control">
                                                            <option value="id">ID</option>
                                                            <option value="xpath">XPath</option>
                                                            <option value="accessibility_id">Accessibility ID</option>
                                                            <option value="class">Class Name</option>
                                                            <option value="name">Name</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifHasAttributeLocatorValue">Element Locator Value</label>
                                                        <input type="text" id="ifHasAttributeLocatorValue" class="form-control" placeholder="Enter the element locator value">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifHasAttributeName">Attribute Name</label>
                                                        <input type="text" id="ifHasAttributeName" class="form-control" placeholder="Enter attribute name (e.g., enabled, disabled, selected)">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifHasAttributeValue">Expected Attribute Value</label>
                                                        <input type="text" id="ifHasAttributeValue" class="form-control" placeholder="Enter expected value (e.g., true, false)">
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifHasAttributeTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifHasAttributeTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>

                                                <!-- Screen Contains Image condition form fields -->
                                                <div id="ifScreenContainsConditionForm" style="display:none;">
                                                    <div class="form-group mb-3">
                                                        <label for="ifScreenContainsImage">Reference Image</label>
                                                        <div class="input-group">
                                                            <select id="ifScreenContainsImage" class="form-control">
                                                                <!-- Will be populated dynamically -->
                                                            </select>
                                                            <button type="button" id="refreshScreenContainsImages" class="btn btn-outline-secondary">
                                                                <i class="bi bi-arrow-clockwise"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <button type="button" id="captureScreenContainsImage" class="btn btn-outline-primary">
                                                            <i class="bi bi-camera"></i> Capture from Screen
                                                        </button>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifScreenContainsThreshold">Match Threshold</label>
                                                        <input type="number" id="ifScreenContainsThreshold" class="form-control" value="0.7" min="0.1" max="1.0" step="0.05">
                                                        <small class="form-text text-muted">Lower values are less strict (0.1-1.0)</small>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="ifScreenContainsTimeout">Timeout (seconds)</label>
                                                        <input type="number" id="ifScreenContainsTimeout" class="form-control" value="10" min="1" step="1">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header bg-light">
                                                Then Action
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group mb-3">
                                                    <label for="thenActionType">Action Type</label>
                                                    <select id="thenActionType" class="form-control">
                                                        <option value="">No Action</option>
                                                        <option value="tap">Tap</option>
                                                        <option value="doubleTap">Double Tap</option>
                                                        <option value="tapImage">Tap Image</option>
                                                        <option value="swipe">Swipe</option>
                                                        <option value="text">Input Text</option>
                                                        <option value="key">Press Key</option>
                                                        <option value="wait">Wait</option>
                                                        <option value="hideKeyboard">Hide Keyboard</option>
                                                        <option value="clickElement">Click Element</option>
                                                        <option value="waitTill">Wait Till Element</option>
                                                        <option value="swipeTillVisible">Swipe Till Visible</option>
                                                        <option value="textClear">Clear & Input Text</option>
                                                        <option value="iosFunctions">iOS Functions</option>
                                                    </select>
                                                </div>

                                                <div id="thenActionFormContainer">
                                                    <!-- This will be dynamically populated based on the selected action type -->
                                                    <div class="alert alert-info">
                                                        Select an action type to configure its parameters
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <!-- Multi Step Action Form -->
                                    <div id="multiStepActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle"></i> This action allows you to include an entire test case as a single step.
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="multiStepTestCase" class="form-label">Select Test Case</label>
                                                    <div class="input-group">
                                                        <select id="multiStepTestCase" name="multiStepTestCase" class="form-select">
                                                            <option value="">-- Select Test Case --</option>
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshMultiStepTestCases">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                <div id="multiStepTestCaseInfo" class="d-none">
                                                    <div class="card mb-3">
                                                        <div class="card-header bg-light">
                                                            <h6 class="mb-0">Test Case Details</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <p><strong>Name:</strong> <span id="multiStepTestCaseName"></span></p>
                                                            <p><strong>Steps:</strong> <span id="multiStepTestCaseSteps"></span></p>
                                                            <p><strong>Description:</strong> <span id="multiStepTestCaseDescription"></span></p>

                                                            <!-- Loading indicator for pre-loading steps -->
                                                            <div id="multiStepLoadingIndicator" class="d-none mt-2">
                                                                <div class="d-flex align-items-center">
                                                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                                        <span class="visually-hidden">Loading...</span>
                                                                    </div>
                                                                    <span>Pre-loading test case steps...</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Repeat Steps Action Form -->
                                    <div id="repeatStepsForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle"></i> This action allows you to repeat a set of steps multiple times.
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="repeatCount" class="form-label">Number of Repetitions</label>
                                                    <input type="number" id="repeatCount" name="repeatCount" class="form-control" min="1" max="100" value="1" placeholder="Enter number of times to repeat">
                                                    <small class="text-muted">How many times should the steps be repeated?</small>
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="repeatStepsTestCase" class="form-label">Select Test Case to Repeat</label>
                                                    <div class="input-group">
                                                        <select id="repeatStepsTestCase" name="repeatStepsTestCase" class="form-select">
                                                            <option value="">-- Select Test Case --</option>
                                                        </select>
                                                        <button class="btn btn-outline-secondary" type="button" id="refreshRepeatStepsTestCases">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                    </div>
                                                    <small class="text-muted">Choose the test case whose steps you want to repeat</small>
                                                </div>

                                                <div id="repeatStepsTestCaseInfo" class="d-none">
                                                    <div class="card mb-3">
                                                        <div class="card-header bg-light">
                                                            <h6 class="mb-0">Test Case Details</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <p><strong>Name:</strong> <span id="repeatStepsTestCaseName"></span></p>
                                                            <p><strong>Steps:</strong> <span id="repeatStepsTestCaseSteps"></span></p>
                                                            <p><strong>Description:</strong> <span id="repeatStepsTestCaseDescription"></span></p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="repeatDelay" class="form-label">Delay Between Repetitions (seconds)</label>
                                                    <input type="number" id="repeatDelay" name="repeatDelay" class="form-control" min="0" max="60" value="0" step="0.1" placeholder="0">
                                                    <small class="text-muted">Optional delay between each repetition (0 for no delay)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hook Action Form -->
                                    <div id="hookActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle"></i> Hook Actions are executed only when a test step fails. After the hook action completes, the failed step will be retried.
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="hookActionType" class="form-label">Select Hook Action Type</label>
                                                    <select id="hookActionType" class="form-select">
                                                        <option value="">-- Select Action Type --</option>
                                                        <option value="tap">Tap</option>
                                                    </select>
                                                </div>

                                                <!-- Container for the selected hook action form -->
                                                <div id="hookActionFormContainer" class="mt-3">
                                                    <!-- The selected action form will be cloned and displayed here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Reporting Action Form -->
                                    <div id="reportingActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle"></i> Reporting actions are used to capture important information and screenshots for test reports. Only steps with action type "reporting" are included in Allure reports.
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="reportingStepName">Step Name</label>
                                                    <input type="text" id="reportingStepName" class="form-control" placeholder="Enter a descriptive name for this step">
                                                    <small class="text-muted">This name will appear in the test report</small>
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="reportingInfo">Info</label>
                                                    <textarea id="reportingInfo" class="form-control" rows="3" placeholder="Enter additional information for this step"></textarea>
                                                    <small class="text-muted">Additional details to include in the report</small>
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="reportingSeverity">Severity</label>
                                                    <select id="reportingSeverity" class="form-control">
                                                        <option value="normal">Normal</option>
                                                        <option value="blocker">Blocker</option>
                                                        <option value="critical">Critical</option>
                                                        <option value="minor">Minor</option>
                                                        <option value="trivial">Trivial</option>
                                                    </select>
                                                    <small class="text-muted">Importance level of this step</small>
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="reportingDescription">Description (Optional)</label>
                                                    <textarea id="reportingDescription" class="form-control" rows="2" placeholder="Enter a detailed description"></textarea>
                                                    <small class="text-muted">Detailed description for the report</small>
                                                </div>

                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="reportingTakeScreenshot" checked>
                                                    <label class="form-check-label" for="reportingTakeScreenshot">
                                                        Take Screenshot
                                                    </label>
                                                    <small class="d-block text-muted">Capture the current screen state</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Take Screenshot Action Form -->
                                    <div id="takeScreenshotActionForm" class="action-form d-none">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-camera"></i> This action will take a full screenshot of the current device screen.
                                                </div>

                                                <div class="form-group mb-3">
                                                    <label for="screenshotName">Screenshot Name</label>
                                                    <input type="text" id="screenshotName" class="form-control" placeholder="Enter a name for the screenshot">
                                                    <small class="text-muted">The screenshot will be saved as app-screenshot_&lt;name&gt;.png</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Fallback Action Section -->
                                    <div id="fallbackActionSection" class="mt-3 mb-3 d-none">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Fallback Action</h6>
                                                <button type="button" class="btn btn-sm btn-danger" id="removeFallbackAction">
                                                    <i class="bi bi-trash"></i> Remove Fallback
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group mb-3">
                                                    <label>Fallback Type</label>
                                                    <select id="fallbackType" class="form-control">
                                                        <option value="">-- Select Fallback Type --</option>
                                                        <option value="coordinates">Click on Coordinates</option>
                                                        <option value="image">Click on Images</option>
                                                        <option value="text">Click on screen text</option>
                                                        <option value="locator">Add Alternate Locator</option>
                                                    </select>
                                                </div>

                                                <!-- Fallback content will be dynamically loaded here -->
                                                <div id="fallbackContent"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <button id="addFallbackAction" class="btn btn-outline-primary me-2">
                                            <i class="bi bi-plus"></i> Add Fallback Action
                                        </button>
                                        <button id="addAction" class="btn btn-success">Add Action</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions List Card -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Actions List</h5>
                                <div>
                                    <button id="saveRecordingBtn" class="btn btn-sm btn-success me-2">
                                        <i class="bi bi-save"></i> Save
                                    </button>
                                    <button id="saveAsRecordingBtn" class="btn btn-sm btn-info me-2" data-bs-toggle="modal" data-bs-target="#saveTestCaseModal">
                                        <i class="bi bi-save"></i> Save As
                                    </button>
                                    <button id="loadRecordingBtn" class="btn btn-sm btn-primary">
                                        <i class="bi bi-folder-open"></i> Load
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div id="actionsList" class="list-group list-group-flush">
                                    <!-- Actions will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Cases tab -->
            <div class="tab-pane fade" id="test-cases-tab" role="tabpanel" style="position: static !important;">
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-code"></i> Test Cases</h5>
                                <div>
                                    <button id="refreshTestCasesBtn" class="btn btn-sm btn-light">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button id="createTestCaseBtn" class="btn btn-sm btn-success ms-2" data-bs-toggle="modal" data-bs-target="#saveTestCaseModal">
                                        <i class="bi bi-plus-circle"></i> Create New
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="test-case-filters">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text bg-white">
                                                    <i class="bi bi-search"></i>
                                                </span>
                                                <input type="text" id="testCaseSearch" class="form-control" placeholder="Search test cases...">
                                                <button class="btn btn-outline-secondary" type="button" id="clearTestCaseSearchBtn">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex justify-content-md-end">
                                                <div class="btn-group" role="group">
                                                    <button id="sortByNameBtn" class="btn btn-outline-primary">
                                                        <i class="bi bi-sort-alpha-down"></i> Name
                                                    </button>
                                                    <button id="sortByDateBtn" class="btn btn-outline-primary active">
                                                        <i class="bi bi-sort-down"></i> Date
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="testCasesContainer">
                                    <div id="testCasesLoading" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-3">Loading test cases...</p>
                                    </div>

                                    <div id="noTestCasesMessage" class="text-center d-none">
                                        <i class="bi bi-file-earmark-x"></i>
                                        <p class="mt-3">No test cases found</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#saveTestCaseModal">
                                            <i class="bi bi-plus-circle"></i> Create Your First Test Case
                                        </button>
                                    </div>

                                    <div id="testCasesList" class="list-group">
                                        <!-- Test cases will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Suites tab -->
            <div class="tab-pane fade" id="test-suites-tab" role="tabpanel" style="position: static !important;">
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-collection"></i> Test Suites</h5>
                                <button id="createTestSuiteBtn" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#createTestSuiteModal">
                                    <i class="bi bi-plus-circle"></i> Create Test Suite
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- Available Test Cases Section -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Available Test Cases</h6>
                                    <div class="list-group" id="availableTestCases">
                                        <!-- Test cases will be populated here -->
                                    </div>
                                </div>

                                <!-- Existing Test Suites Section -->
                                <div>
                                    <h6 class="mb-3">Existing Test Suites</h6>
                                    <div class="list-group" id="testSuitesList">
                                        <!-- Test suites will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Test Suite Modal -->
            <div class="modal fade" id="createTestSuiteModal" tabindex="-1" aria-labelledby="createTestSuiteModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="createTestSuiteModalLabel">Create New Test Suite</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createTestSuiteForm">
                                <div class="mb-3">
                                    <label for="testSuiteName" class="form-label">Test Suite Name</label>
                                    <input type="text" class="form-control" id="testSuiteName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="testSuiteDescription" class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" id="testSuiteDescription" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Add Test Cases</label>
                                    <div class="input-group mb-3">
                                        <select id="testCaseSelector" class="form-select">
                                            <option value="" selected disabled>Select a test case to add</option>
                                            <!-- Test cases will be populated here -->
                                        </select>
                                        <button class="btn btn-outline-primary" type="button" id="addTestCaseBtn">
                                            <i class="bi bi-plus"></i> Add
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Selected Test Cases</label>
                                    <p class="text-muted small">Drag and drop to reorder test cases</p>
                                    <div id="selectedTestCases" class="list-group sortable-test-cases">
                                        <!-- Selected test cases will be listed here -->
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveTestSuiteBtn">Save Test Suite</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings tab -->
            <div class="tab-pane fade" id="settings-tab" role="tabpanel" aria-labelledby="settings-tab-btn" style="position: static !important;">
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear-fill me-2"></i>Application Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="settingsForm">
                                    <!-- Global Values Section -->
                                    <div class="card mb-4">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Global Values</h6>
                                                <div>
                                                    <button type="button" id="saveGlobalParamsBtn" class="btn btn-sm btn-outline-primary me-1">
                                                        <i class="bi bi-save"></i> Save Values
                                                    </button>
                                                    <button type="button" id="refreshGlobalParamsBtn" class="btn btn-sm btn-outline-secondary">
                                                        <i class="bi bi-arrow-repeat"></i> Refresh
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> <strong>Default Element Timeout</strong> controls how long actions will wait for elements to appear before failing (in seconds).
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0">Parameter List</h6>
                                                    <button type="button" id="addGlobalParameterBtn" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-plus"></i> Add Parameter
                                                    </button>
                                                </div>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="globalValuesTable">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 40%">Parameter Name</th>
                                                                <th style="width: 50%">Parameter Value</th>
                                                                <th style="width: 10%">Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="globalValuesTableBody">
                                                            <!-- Parameters will be populated dynamically -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Folder Settings Section -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Test Cases Directory</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="testCasesDir" class="form-label">Directory Path</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="testCasesDir" name="testcases_dir" placeholder="Path to test cases directory">
                                                            <button class="btn btn-outline-secondary" type="button" id="validateTestCasesBtn">
                                                                <i class="bi bi-check2-circle"></i> Validate
                                                            </button>
                                                        </div>
                                                        <div class="form-text">Default: "test_cases" in root directory</div>
                                                        <div id="testCasesValidationResult" class="mt-2 validation-result"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Reports Directory</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="reportsDir" class="form-label">Directory Path</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="reportsDir" name="reports_dir" placeholder="Path to reports directory">
                                                            <button class="btn btn-outline-secondary" type="button" id="validateReportsBtn">
                                                                <i class="bi bi-check2-circle"></i> Validate
                                                            </button>
                                                        </div>
                                                        <div class="form-text">Default: "reports" in root directory</div>
                                                        <div id="reportsValidationResult" class="mt-2 validation-result"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Reference Images Directory</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="referenceImagesDir" class="form-label">Directory Path</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="referenceImagesDir" name="reference_images_dir" placeholder="Path to reference images directory">
                                                            <button class="btn btn-outline-secondary" type="button" id="validateRefImagesBtn">
                                                                <i class="bi bi-check2-circle"></i> Validate
                                                            </button>
                                                        </div>
                                                        <div class="form-text">Default: "reference_images" in root directory</div>
                                                        <div id="refImagesValidationResult" class="mt-2 validation-result"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Files To Push Directory</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="filesToPushDir" class="form-label">Directory Path</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="filesToPushDir" name="files_to_push_dir" placeholder="Path to files to push directory">
                                                            <button class="btn btn-outline-secondary" type="button" id="validateFilesToPushBtn">
                                                                <i class="bi bi-check2-circle"></i> Validate
                                                            </button>
                                                        </div>
                                                        <div class="form-text">Default: "files_to_push" in root directory</div>
                                                        <div id="filesToPushValidationResult" class="mt-2 validation-result"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Test Suites Directory</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="testSuitesDir" class="form-label">Directory Path</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" id="testSuitesDir" name="test_suites_dir" placeholder="Path to test suites directory">
                                                            <button class="btn btn-outline-secondary" type="button" id="validateTestSuitesBtn">
                                                                <i class="bi bi-check2-circle"></i> Validate
                                                            </button>
                                                        </div>
                                                        <div class="form-text">Default: "test_suites" in root directory</div>
                                                        <div id="testSuitesValidationResult" class="mt-2 validation-result"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-end mt-3">
                                        <button type="button" id="resetSettingsBtn" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-arrow-counterclockwise"></i> Reset to Defaults
                                        </button>
                                        <button type="button" id="saveSettingsBtn" class="btn btn-primary">
                                            <i class="bi bi-save"></i> Save Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Environments Tab Pane -->
            <div class="tab-pane fade" id="environments-tab-pane" role="tabpanel" aria-labelledby="environments-tab-btn">
                {% include 'environments.html' %}
            </div>

        </div>
    </div>

    <!-- Element Inspector Modal -->
    <div class="modal fade" id="elementInspectorModal" tabindex="-1" aria-labelledby="elementInspectorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title" id="elementInspectorModalLabel"><i class="bi bi-search"></i> Element Inspector</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="element-inspector">
                        <!-- Element Type Display -->
                        <div class="p-2 bg-light border-bottom">
                            <span class="badge bg-primary me-2">Type</span>
                            <span id="elementTypeDisplay">Unknown</span>
                            <span class="ms-3 badge bg-secondary me-2">Bounds</span>
                            <span id="elementBounds">Unknown</span>
                        </div>

                        <!-- Element Summary -->
                        <div id="elementSummary" class="p-2 border-bottom"></div>

                        <!-- Add Tabs Navigation -->
                        <ul class="nav nav-tabs" id="inspectorTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="app-source-tab" data-bs-toggle="tab" data-bs-target="#app-source-tab-pane" type="button" role="tab" aria-controls="app-source-tab-pane" aria-selected="true">App Source</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="selected-element-tab" data-bs-toggle="tab" data-bs-target="#selected-element-tab-pane" type="button" role="tab" aria-controls="selected-element-tab-pane" aria-selected="false">Selected Element</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="locators-tab" data-bs-toggle="tab" data-bs-target="#locators-tab-pane" type="button" role="tab" aria-controls="locators-tab-pane" aria-selected="false">Locators</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="attributes-tab" data-bs-toggle="tab" data-bs-target="#attributes-tab-pane" type="button" role="tab" aria-controls="attributes-tab-pane" aria-selected="false">Attributes</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="session-info-tab" data-bs-toggle="tab" data-bs-target="#session-info-tab-pane" type="button" role="tab" aria-controls="session-info-tab-pane" aria-selected="false">Session Info</button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="inspectorTabContent">
                            <!-- App Source Tab -->
                            <div class="tab-pane fade show active" id="app-source-tab-pane" role="tabpanel" aria-labelledby="app-source-tab" tabindex="0">
                        <div class="p-3">
                                    <h6 class="mb-3"><i class="bi bi-code"></i> App Source</h6>
                                    <div id="appSourceContainer" class="source-container">
                                        <div class="d-flex justify-content-center align-items-center my-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <span class="ms-2">Loading app source...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Selected Element Tab -->
                            <div class="tab-pane fade" id="selected-element-tab-pane" role="tabpanel" aria-labelledby="selected-element-tab" tabindex="0">
                                <div class="p-3">
                                    <h6 class="mb-3"><i class="bi bi-search"></i> Selected Element</h6>
                                    <div class="row mb-3">
                                        <div class="col-md-3">
                                            <div class="text-secondary">Find By</div>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="text-secondary">Selector</div>
                                        </div>
                                    </div>

                                    <!-- Accessibility ID -->
                                    <div class="row mb-2">
                                        <div class="col-md-3">accessibility id</div>
                                        <div class="col-md-9">
                                            <code id="accessibilityIdSelector">-</code>
                                        </div>
                                    </div>

                                    <!-- iOS Class Chain -->
                                    <div class="row mb-2 ios-only">
                                        <div class="col-md-3">-ios class chain</div>
                                        <div class="col-md-9">
                                            <code id="iOSClassChainSelector">-</code>
                                        </div>
                                    </div>

                                    <!-- iOS Predicate String -->
                                    <div class="row mb-2 ios-only">
                                        <div class="col-md-3">-ios predicate string</div>
                                        <div class="col-md-9">
                                            <code id="iOSPredicateSelector">-</code>
                                        </div>
                                    </div>

                                    <!-- XPath -->
                                    <div class="row mb-2">
                                        <div class="col-md-3">xpath</div>
                                        <div class="col-md-9">
                                            <code id="xpathSelector">-</code>
                                        </div>
                                    </div>

                                    <!-- Element Attributes -->
                                    <div class="mt-4">
                                        <h6 class="mb-3"><i class="bi bi-list-ul"></i> Element Attributes</h6>
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Attribute</th>
                                                    <th>Value</th>
                                                </tr>
                                            </thead>
                                            <tbody id="elementAttributesTable">
                                                <!-- Attributes will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Locators Tab Pane -->
                            <div class="tab-pane fade" id="locators-tab-pane" role="tabpanel" aria-labelledby="locators-tab" tabindex="0">
                                <div class="p-3">
                                    <h6><i class="bi bi-geo-alt"></i> Locator Strategies</h6>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>ID</strong>
                                </div>
                                <div class="card-body p-0">
                                    <div id="idLocators" class="locator-list"></div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>XPath</strong>
                                </div>
                                <div class="card-body p-0">
                                    <div id="xpathLocators" class="locator-list"></div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>Accessibility ID</strong>
                                </div>
                                <div class="card-body p-0">
                                    <div id="accessibilityLocators" class="locator-list"></div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>Text</strong>
                                </div>
                                <div class="card-body p-0">
                                    <div id="textLocators" class="locator-list"></div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>Position</strong>
                                </div>
                                <div class="card-body p-0">
                                            <div id="positionLocators" class="locator-list"></div>
                                </div>
                                    </div>

                                    <!-- iOS-specific locators -->
                                    <div class="card mb-3 ios-only" style="display: none;">
                                        <div class="card-header bg-light">
                                            <strong>iOS Class Chain</strong>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="iosClassChainLocators" class="locator-list"></div>
                            </div>
                        </div>

                                    <div class="card mb-3 ios-only" style="display: none;">
                                        <div class="card-header bg-light">
                                            <strong>iOS Predicate String</strong>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="iosPredicateLocators" class="locator-list"></div>
                                        </div>
                        </div>

                                    <!-- Android-specific locators -->
                                    <div class="card mb-3 android-only" style="display: none;">
                                        <div class="card-header bg-light">
                                            <strong>Android UiAutomator</strong>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="androidUiAutomatorLocators" class="locator-list"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Attributes Tab Pane -->
                            <div class="tab-pane fade" id="attributes-tab-pane" role="tabpanel" aria-labelledby="attributes-tab" tabindex="0">
                                <div class="p-3">
                                    <h6><i class="bi bi-list-ul"></i> All Attributes</h6>
                                    <div id="attributesList" class="attributes-table">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Value</th>
                                                </tr>
                                            </thead>
                                            <tbody id="allAttributesTable">
                                                <!-- All attributes will be listed here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Session Info Tab Pane -->
                            <div class="tab-pane fade" id="session-info-tab-pane" role="tabpanel" aria-labelledby="session-info-tab" tabindex="0">
                                <div class="p-3">
                                    <h6 class="mb-3"><i class="bi bi-info-circle"></i> Session ID</h6>
                                    <p><code id="sessionId">Loading...</code></p>

                                    <h6 class="mb-3 mt-4"><i class="bi bi-gear"></i> Capabilities</h6>
                                    <pre id="sessionCapabilities" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">Loading...</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="dropdown me-auto">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-plus-circle"></i> Add Action
                    </button>
                        <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" id="addElementTapAction"><i class="bi bi-hand-index-thumb"></i> Tap</a></li>
                        <li><a class="dropdown-item" href="#" id="addElementSwipeAction"><i class="bi bi-arrow-right"></i> Swipe</a></li>
                        <li><a class="dropdown-item" href="#" id="addElementTextAction"><i class="bi bi-keyboard"></i> Input Text</a></li>
                        <li><a class="dropdown-item" href="#" id="addInputTextAction"><i class="bi bi-pencil-square"></i> Send Keys</a></li>
                        <li><a class="dropdown-item" href="#" id="addClearAction"><i class="bi bi-x-lg"></i> Clear</a></li>
                        <li><a class="dropdown-item" href="#" id="addLongTapAction"><i class="bi bi-hand-index-thumb-fill"></i> Long Press</a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" id="continueInspectingBtn" class="btn btn-success">Continue Inspecting</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Test Case Modal -->
    <div class="modal fade" id="saveTestCaseModal" tabindex="-1" aria-labelledby="saveTestCaseModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="saveTestCaseModalLabel"><i class="bi bi-save"></i> Save Test Case</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="testCaseName" class="form-label">Test Case Name</label>
                        <input type="text" class="form-control" id="testCaseName" placeholder="Enter a name for this test case" autocomplete="off">
                    </div>
                    <div class="mb-3">
                        <label for="testCaseLabels" class="form-label">Labels</label>
                        <input type="text" class="form-control" id="testCaseLabels" placeholder="Enter comma-separated labels (e.g. regression, smoke-test)" autocomplete="off">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmSaveTestCase" class="btn btn-success">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Test Case Modal - Updated structure -->
    <div class="modal fade" id="loadTestCaseModal" tabindex="-1" aria-labelledby="loadTestCaseModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="loadTestCaseModalLabel"><i class="bi bi-folder-open"></i> Load Test Case</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" id="loadTestCaseCloseBtn"></button>
                </div>
                <div class="modal-body">
                    <div id="testCasesList" class="list-group mb-3">
                        <!-- Test cases will be populated here -->
                        <div class="text-center p-3" id="loadingTestCases">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading test cases...</p>
                        </div>
                        <div class="text-center p-3 d-none" id="noTestCases">
                            <i class="bi bi-exclamation-circle text-muted fs-1"></i>
                            <p class="mt-2">No test cases found</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="loadTestCaseCancelBtn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/mode/python/python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- Hidden file inputs for directory browsing -->
    <input type="file" id="testCasesDirInput" webkitdirectory directory style="display: none;">
    <input type="file" id="reportsDirInput" webkitdirectory directory style="display: none;">

    <!-- Custom JS -->
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/action-manager.js"></script>
    <script src="/static/js/modules/ElementInteractions.js"></script>
    <script src="/static/js/fixed-device-screen.js"></script>
    <script src="/static/js/execution-overlay.js"></script>
    <script src="/static/js/execution-manager.js"></script>
    <script src="/static/js/modules/TestCaseManager.js"></script>
    <script src="/static/js/action-description.js"></script>
    <script src="/static/js/multi-step-action.js"></script>
    <script src="/static/js/hook-action.js"></script>
    <script src="/static/js/modules/fallback-locators.js"></script>
    <script src="/static/js/modules/tap-fallback-manager.js"></script>
    <script src="/static/js/random-data-generator.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="{{ url_for('static', filename='js/test_suites.js') }}"></script>
    <!-- Reports JS removed as it's no longer needed -->
    <script src="{{ url_for('static', filename='js/settings.js') }}"></script>

    <!-- Fix for tab transitions -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fix for tab transitions
            const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
            tabLinks.forEach(tabLink => {
                tabLink.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-bs-target');
                    const targetTab = document.querySelector(targetId);
                    if (targetTab) {
                        // Force position to be static
                        targetTab.style.position = 'static';
                        targetTab.style.marginTop = '0';
                    }
                });
            });
        });
    </script>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer" style="z-index: 1100"></div>

    <!-- Include the Tap Fallback Modal -->
    {% include 'tap-fallback-modal.html' %}

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Helper function to initialize or update a Bootstrap Popover
            function showEnvVarPopover(element, content) {
                if (!bootstrap || !bootstrap.Popover) {
                    console.warn("Bootstrap Popover not available for env var hints.");
                    return;
                }
                let popover = bootstrap.Popover.getInstance(element);
                if (popover) {
                    popover.setContent({ '.popover-body': content });
                    // If popover was manually hidden or auto-hid, ensure it can be reshown
                    if (element.hasAttribute('aria-describedby')) { // Indicates popover has been initialized
                         // No direct method to check if hidden other than re-showing if needed
                    }
                } else {
                    popover = new bootstrap.Popover(element, {
                        trigger: 'manual', // We will control show/hide manually
                        placement: 'top',
                        html: true,
                        sanitize: false,
                        content: content,
                        customClass: 'env-var-popover',
                        offset: [0, 8] // Optional: small offset
                    });
                }
                // Only show if input is focused and content is meaningful
                if (document.activeElement === element && content && !content.includes("No env variable detected")) {
                     if(popover) popover.show();
                } else if (popover) {
                    popover.hide();
                }
            }

            // Debounce function
            function debounce(func, delay) {
                let timeout;
                return function(...args) {
                    const context = this;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(context, args), delay);
                };
            }

            async function handleEnvVarHoverResolution(inputElement) {
                const text = inputElement.value;
                // Regex to find the env[...] placeholder that the cursor is currently inside or immediately after
                // This is a simplified approach: find the last one for now.
                const envVarRegex = /env\[([^[\]]+)\]/g; // Simpler: env[var_name]
                let lastMatch = null;
                let match;
                while ((match = envVarRegex.exec(text)) !== null) {
                    lastMatch = match;
                }

                let popover = bootstrap.Popover.getInstance(inputElement);

                if (lastMatch) {
                    const placeholder = lastMatch[0];
                    // const variableName = lastMatch[1]; // Not directly used in this version of API call

                    try {
                        const response = await fetch('/api/environments/resolve_hover_variable', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ placeholder: placeholder })
                        });
                        const result = await response.json();
                        let popoverContent = '';

                        if (response.ok) {
                            if (result.status === 'resolved') {
                                popoverContent = `<strong>${placeholder}</strong> &rarr; <code class="text-success">${result.resolved_value}</code>`;
                            } else if (result.status === 'not_found') {
                                popoverContent = `<strong>${placeholder}</strong>: <span class="text-warning">Variable not found in current environment.</span>`;
                            } else if (result.status === 'error' && result.message && result.message.toLowerCase().includes('environment id') && result.message.toLowerCase().includes('not found')) {
                                popoverContent = `<strong>${placeholder}</strong>: <span class="text-danger">Current environment (ID: ${result.environment_id}) not found.</span>`;
                            } else {
                                popoverContent = `<strong>${placeholder}</strong>: <span class="text-danger">${result.error || 'Could not resolve.'}</span>`;
                            }
                        } else {
                            popoverContent = `<strong>${placeholder}</strong>: <span class="text-danger">Error: ${result.error || 'Failed to resolve.'}</span>`;
                        }
                        showEnvVarPopover(inputElement, popoverContent);
                    } catch (error) {
                        console.error('Error resolving env var for hover:', error);
                        showEnvVarPopover(inputElement, `<strong>${placeholder || 'env[...]'}</strong>: <span class="text-danger">Network error.</span>`);
                    }
                } else {
                    if (popover) { // Hide if no env var syntax detected
                        popover.hide();
                    }
                }
            }

            const debouncedHandleEnvVarHoverResolution = debounce(handleEnvVarHoverResolution, 400);

            function enableEnvVariableHover(inputElement) {
                if (!inputElement || inputElement.dataset.envHoverEnabled === 'true') return;

                inputElement.addEventListener('focus', function() {
                    debouncedHandleEnvVarHoverResolution(this);
                });
                inputElement.addEventListener('keyup', function(event) {
                    // Allow navigation keys without triggering resolution
                    if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Shift', 'Control', 'Alt', 'Meta', 'Tab', 'Escape'].includes(event.key)) {
                       debouncedHandleEnvVarHoverResolution(this);
                    }
                });
                inputElement.addEventListener('input', function() { // Handles paste, cut, etc.
                    debouncedHandleEnvVarHoverResolution(this);
                });
                inputElement.addEventListener('blur', function() {
                    setTimeout(() => { // Delay to allow click on popover if it were interactive
                        let popover = bootstrap.Popover.getInstance(this);
                        if (popover) {
                            popover.hide();
                        }
                    }, 150);
                });
                inputElement.dataset.envHoverEnabled = 'true'; // Mark as enabled
            }

            const potentialEnvVarInputIds = [
                'tapLocatorValue', 'tapImageName', 'tapTextToFind', 
                'textInput', // This is the ID for 'Input Text' action's main field
                'swipeStartX', 'swipeStartY', 'swipeEndX', 'swipeEndY', 'swipeDuration',
                'sendKeysInput', 'sendKeysLocatorValue',
                'addLogMessage',
                'tapIfImageExistsImageName', 'tapIfImageExistsTimeout',
                'keyValue',
                'waitTime',
                'getValueLocatorValue', 'getValueAttribute', 'getValueParamName',
                'compareValueLocatorValue', 'compareValueAttribute', 'compareValueExpected', 'compareValueTimeout',
                'getParamName', 'getParamDefaultValue',
                'setParamName', 'setParamValue',
                'launchAppBundleId',
                'terminateAppBundleId',
                'uninstallAppBundleId',
                'swipeTillVisibleDirection', 'swipeTillVisibleStartX', 'swipeTillVisibleStartY', 'swipeTillVisibleEndX', 'swipeTillVisibleEndY',
                'swipeTillVisibleLocatorValue', 'swipeTillVisibleTextToFind', 'swipeTillVisibleTimeout', 'swipeTillVisibleMaxSwipes',
                'waitTillLocatorValue', 'waitTillImageName', 'waitTillTextToFind', 'waitTillTimeout',
                'existsLocatorValue', 'existsImageName', 'existsTextToFind', 'existsTimeout',
                'textClearTextToInput', 'textClearLocatorValue', 'textClearSkipLocatorValue',
                'tapAndTypeLocatorValue', 'tapAndTypeTextToInput',
                'clipboardContent', 'iosKeyName', 'iosAlertButton', 'iosAlertTimeout',
                'ifExistsLocatorValue', 'ifExistsImageName', 'ifExistsTextToFind', 'ifExistsTimeout',
                'ifVisibleLocatorValue', 'ifVisibleTimeout',
                'ifContainsTextLocatorValue', 'ifContainsTextExpectedText', 'ifContainsTextTimeout',
                'ifValueEqualsLocatorValue', 'ifValueEqualsExpectedValue', 'ifValueEqualsTimeout',
                'ifValueContainsLocatorValue', 'ifValueContainsExpectedValue', 'ifValueContainsTimeout',
                'ifHasAttributeLocatorValue', 'ifHasAttributeName', 'ifHasAttributeTimeout',
                'hookActionID', 'hookConditionValue', 'hookActionValue', // Added hookActionValue
                // Specific inputs from various action forms that might take text
                'customSwipeCoordinates', 'customDirectionCoordinates', 'alertAction', 'alertText',
                'textClearLocatorValueToSkip', // Assuming this might exist based on textClearSkipLocatorValue
                'findElementLocatorValue', 'imageRecognitionThreshold', 'imageRecognitionTimeout',
                'assertValueLocatorValue', 'assertValueAttribute', 'assertValueExpectedValue',
                'executeScriptCode',
                'commentText' // If there's a comment action or field
            ];
            
            // This covers inputs present at load time.
            potentialEnvVarInputIds.forEach(id => {
                const inputEl = document.getElementById(id);
                if (inputEl && (inputEl.tagName === 'INPUT' || inputEl.tagName === 'TEXTAREA')) {
                    enableEnvVariableHover(inputEl);
                }
            });

            // For dynamically added inputs (e.g. in test case steps if they become editable in the future)
            // or for inputs that might not have static IDs but share a class.
            // Using a mutation observer is more robust for future dynamic content.
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // Check if the node itself is an input we care about
                                if ((node.tagName === 'INPUT' || node.tagName === 'TEXTAREA') && potentialEnvVarInputIds.includes(node.id)) {
                                    enableEnvVariableHover(node);
                                }
                                // Check its descendants
                                node.querySelectorAll('input[type="text"], textarea').forEach(inputElement => {
                                    // Check if ID is in our list or if it has a specific class for env vars
                                    if (potentialEnvVarInputIds.includes(inputElement.id) /* || inputElement.classList.contains('env-var-input') */ ) {
                                        enableEnvVariableHover(inputElement);
                                    }
                                });
                            }
                        });
                    }
                });
            });

            // Start observing the document body for added nodes.
            // Adjust the target node if action forms are loaded into a specific container.
            const config = { childList: true, subtree: true };
            const targetNode = document.getElementById('actionBuilderCollapse') || document.body; // More specific if possible
            observer.observe(targetNode, config);

        });
    </script>
</body>
</html>