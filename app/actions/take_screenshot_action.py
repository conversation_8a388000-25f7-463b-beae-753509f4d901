from .base_action import BaseAction
import logging
import os
import time
from datetime import datetime

class TakeScreenshotAction(BaseAction):
    """Handler for taking screenshots with custom names"""

    def execute(self, params):
        """
        Execute take screenshot action

        Args:
            params: Dictionary containing:
                - screenshot_name: The name for the screenshot (required)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get screenshot name from parameters
        screenshot_name = params.get('screenshot_name', '').strip()
        if not screenshot_name:
            return {"status": "error", "message": "Screenshot name is required"}

        try:
            # Validate screenshot name uniqueness
            if not self._is_screenshot_name_unique(screenshot_name):
                return {"status": "error", "message": f"Screenshot name '{screenshot_name}' already exists. Please use a unique name."}

            # Get action_id from params for screenshot naming
            action_id = params.get('action_id', '')

            # Take screenshot using the controller - it returns a dict
            screenshot_result = self.controller.take_screenshot(action_id=action_id)

            if not screenshot_result or screenshot_result.get('status') != 'success':
                error_msg = screenshot_result.get('message', 'Failed to take screenshot') if screenshot_result else 'Failed to take screenshot'
                return {"status": "error", "message": error_msg}

            # Get the path from the result
            screenshot_path = screenshot_result.get('path')
            if not screenshot_path or not os.path.exists(screenshot_path):
                return {"status": "error", "message": "Screenshot file not found after capture"}

            # Create the new filename with the custom name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"app-screenshot_{screenshot_name}_{timestamp}.png"

            # Get the directory of the original screenshot
            screenshot_dir = os.path.dirname(screenshot_path)
            new_path = os.path.join(screenshot_dir, new_filename)

            # Copy the screenshot to create a separate file with custom name
            # Keep the original action_id based screenshot and create an additional one
            try:
                import shutil
                shutil.copy2(screenshot_path, new_path)
                self.logger.info(f"Additional screenshot saved as: {new_filename}")

                return {
                    "status": "success",
                    "message": f"Screenshot taken and saved as {new_filename} (action_id: {action_id})",
                    "screenshot_path": new_path,
                    "action_id_path": screenshot_path
                }
            except OSError as e:
                self.logger.error(f"Failed to copy screenshot file: {e}")
                # If copy fails, just return the original path
                return {
                    "status": "success",
                    "message": f"Screenshot taken with action_id {action_id} (custom name copy failed)",
                    "screenshot_path": screenshot_path
                }

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            return {"status": "error", "message": f"Screenshot action failed: {str(e)}"}

    def _is_screenshot_name_unique(self, screenshot_name):
        """
        Check if the screenshot name is unique by looking for existing files
        with the same base name pattern.
        """
        try:
            # Get screenshots directory from config
            from app.config import SCREENSHOTS_DIR

            # Look for existing files with the same base name pattern
            pattern = f"app-screenshot_{screenshot_name}_*.png"
            import glob
            existing_files = glob.glob(os.path.join(SCREENSHOTS_DIR, pattern))

            # Return True if no existing files found
            return len(existing_files) == 0

        except Exception as e:
            self.logger.error(f"Error checking screenshot name uniqueness: {e}")
            # If we can't check, allow it to proceed
            return True
