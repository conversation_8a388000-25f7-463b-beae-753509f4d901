from .base_action import BaseAction
import logging
import os
import time
from datetime import datetime

class TakeScreenshotAction(BaseAction):
    """Handler for taking screenshots with custom names"""

    @staticmethod
    def validate_screenshot_name(screenshot_name):
        """
        Validate screenshot name uniqueness for test case creation.
        This should be called from the UI when adding a take screenshot action.

        Args:
            screenshot_name (str): The screenshot name to validate

        Returns:
            dict: {'valid': bool, 'message': str}
        """
        try:
            if not screenshot_name or not screenshot_name.strip():
                return {'valid': False, 'message': 'Screenshot name is required'}

            screenshot_name = screenshot_name.strip()

            # Get screenshots directory from config
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from config import DIRECTORIES

            screenshots_dir = DIRECTORIES['SCREENSHOTS']

            # Look for existing files with the exact name (simple format)
            exact_file = os.path.join(str(screenshots_dir), f"{screenshot_name}.png")
            file_exists = os.path.exists(exact_file)

            if file_exists:
                return {'valid': False, 'message': f"Screenshot name '{screenshot_name}' already exists. Please use a unique name."}
            else:
                return {'valid': True, 'message': 'Screenshot name is available'}

        except Exception as e:
            # If we can't check, allow it to proceed
            return {'valid': True, 'message': 'Unable to validate, but proceeding'}

    def execute(self, params):
        """
        Execute take screenshot action

        Args:
            params: Dictionary containing:
                - screenshot_name: The name for the screenshot (required)

        Returns:
            dict: Result with status and message
        """
        # Add debug logging
        self.logger.info(f"TakeScreenshotAction.execute called with params: {params}")
        self.logger.info(f"Controller available: {self.controller is not None}")
        if self.controller:
            self.logger.info(f"Controller type: {type(self.controller)}")

        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get screenshot name from parameters
        screenshot_name = params.get('screenshot_name', '').strip()
        if not screenshot_name:
            return {"status": "error", "message": "Screenshot name is required"}

        try:
            # During execution, skip validation - validation should only happen during test case creation
            # Get action_id from params for screenshot naming
            action_id = params.get('action_id', '')

            # Take screenshot using the controller - it returns a dict
            screenshot_result = self.controller.take_screenshot(action_id=action_id)

            if not screenshot_result:
                return {"status": "error", "message": "Failed to take screenshot - no result returned"}

            # Check if the screenshot operation was successful or partially successful
            status = screenshot_result.get('status')
            if status not in ['success', 'partial_success']:
                error_msg = screenshot_result.get('message', 'Failed to take screenshot')
                return {"status": "error", "message": error_msg}

            # Get the path from the result
            screenshot_path = screenshot_result.get('path')
            if not screenshot_path:
                return {"status": "error", "message": "No screenshot path returned"}

            # If the file doesn't exist but we have a path, try to take a new screenshot
            if not os.path.exists(screenshot_path):
                self.logger.warning(f"Screenshot path {screenshot_path} doesn't exist, attempting to take new screenshot")
                # Force a new screenshot by not using the action_id (to avoid database lookup)
                new_screenshot_result = self.controller.take_screenshot()

                if new_screenshot_result and new_screenshot_result.get('status') in ['success', 'partial_success']:
                    new_path = new_screenshot_result.get('path')
                    if new_path and os.path.exists(new_path):
                        # Copy the new screenshot to the expected action_id path
                        try:
                            import shutil
                            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                            shutil.copy2(new_path, screenshot_path)
                            self.logger.info(f"Copied new screenshot to action_id path: {screenshot_path}")
                        except Exception as copy_err:
                            self.logger.error(f"Failed to copy new screenshot: {copy_err}")
                            # Use the new path instead
                            screenshot_path = new_path
                    else:
                        return {"status": "error", "message": "Failed to take new screenshot after original file not found"}
                else:
                    return {"status": "error", "message": "Screenshot file not found and failed to take new screenshot"}

            # Create the new filename with the custom name (simple format)
            new_filename = f"{screenshot_name}.png"

            # Get the directory of the original screenshot
            screenshot_dir = os.path.dirname(screenshot_path)
            new_path = os.path.join(screenshot_dir, new_filename)

            # If the file already exists in the same directory, make it unique
            if os.path.exists(new_path):
                counter = 1
                base_name = screenshot_name
                while os.path.exists(new_path):
                    new_filename = f"{base_name}_{counter}.png"
                    new_path = os.path.join(screenshot_dir, new_filename)
                    counter += 1
                self.logger.info(f"File already exists, using unique name: {new_filename}")

            # Copy the screenshot to create a separate file with custom name
            # Keep the original action_id based screenshot and create an additional one
            try:
                import shutil
                shutil.copy2(screenshot_path, new_path)
                self.logger.info(f"Additional screenshot saved as: {new_filename}")

                # Also copy the custom screenshot to the test suite folder
                suite_screenshot_path = self._copy_to_suite_folder(new_path, screenshot_name)

                return {
                    "status": "success",
                    "message": f"Screenshot taken and saved as {new_filename} (action_id: {action_id})",
                    "screenshot_path": new_path,
                    "action_id_path": screenshot_path,
                    "suite_screenshot_path": suite_screenshot_path,
                    "custom_screenshot_name": screenshot_name,
                    "custom_screenshot_filename": new_filename
                }
            except OSError as e:
                self.logger.error(f"Failed to copy screenshot file: {e}")
                # If copy fails, just return the original path
                return {
                    "status": "success",
                    "message": f"Screenshot taken with action_id {action_id} (custom name copy failed)",
                    "screenshot_path": screenshot_path
                }

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            return {"status": "error", "message": f"Screenshot action failed: {str(e)}"}

    def _copy_to_suite_folder(self, screenshot_path, screenshot_name):
        """
        Copy the custom screenshot to the test suite folder

        Args:
            screenshot_path (str): Path to the screenshot file
            screenshot_name (str): Name of the screenshot

        Returns:
            str or None: Path to the copied screenshot in suite folder, or None if failed
        """
        try:
            # Get the test suites directory from config
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from config import DIRECTORIES

            test_suites_dir = DIRECTORIES['TEST_SUITES']

            # Create a screenshots subfolder in the test suites directory
            suite_screenshots_dir = os.path.join(str(test_suites_dir), 'screenshots')
            os.makedirs(suite_screenshots_dir, exist_ok=True)

            # Create the destination path
            suite_screenshot_path = os.path.join(suite_screenshots_dir, f"{screenshot_name}.png")

            # Copy the screenshot
            import shutil
            shutil.copy2(screenshot_path, suite_screenshot_path)

            self.logger.info(f"Screenshot copied to test suite folder: {suite_screenshot_path}")
            return suite_screenshot_path

        except Exception as e:
            self.logger.error(f"Failed to copy screenshot to suite folder: {e}")
            return None


