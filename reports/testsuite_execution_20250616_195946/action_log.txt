Action Log - 2025-06-16 20:00:54
================================================================================

[[20:00:53]] [INFO] Generating execution report...
[[20:00:53]] [SUCCESS] All tests passed successfully!
[[20:00:53]] [SUCCESS] Screenshot refreshed
[[20:00:53]] [INFO] Refreshing screenshot...
[[20:00:51]] [SUCCESS] Screenshot refreshed successfully
[[20:00:51]] [SUCCESS] Screenshot refreshed successfully
[[20:00:51]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[20:00:50]] [SUCCESS] Screenshot refreshed
[[20:00:50]] [INFO] Refreshing screenshot...
[[20:00:48]] [SUCCESS] Screenshot refreshed successfully
[[20:00:48]] [SUCCESS] Screenshot refreshed successfully
[[20:00:47]] [INFO] Executing action 18/19: takeScreenshot action
[[20:00:47]] [SUCCESS] Screenshot refreshed
[[20:00:47]] [INFO] Refreshing screenshot...
[[20:00:43]] [SUCCESS] Screenshot refreshed successfully
[[20:00:43]] [SUCCESS] Screenshot refreshed successfully
[[20:00:43]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[20:00:43]] [SUCCESS] Screenshot refreshed
[[20:00:43]] [INFO] Refreshing screenshot...
[[20:00:40]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:00:40]] [SUCCESS] Screenshot refreshed successfully
[[20:00:40]] [SUCCESS] Screenshot refreshed successfully
[[20:00:39]] [SUCCESS] Screenshot refreshed
[[20:00:39]] [INFO] Refreshing screenshot...
[[20:00:38]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:00:38]] [SUCCESS] Screenshot refreshed successfully
[[20:00:38]] [SUCCESS] Screenshot refreshed successfully
[[20:00:37]] [SUCCESS] Screenshot refreshed
[[20:00:37]] [INFO] Refreshing screenshot...
[[20:00:34]] [SUCCESS] Screenshot refreshed successfully
[[20:00:34]] [SUCCESS] Screenshot refreshed successfully
[[20:00:34]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:00:34]] [SUCCESS] Screenshot refreshed
[[20:00:34]] [INFO] Refreshing screenshot...
[[20:00:31]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:00:31]] [SUCCESS] Screenshot refreshed successfully
[[20:00:31]] [SUCCESS] Screenshot refreshed successfully
[[20:00:30]] [SUCCESS] Screenshot refreshed
[[20:00:30]] [INFO] Refreshing screenshot...
[[20:00:28]] [INFO] Executing action 12/19: takeScreenshot action
[[20:00:28]] [SUCCESS] Screenshot refreshed successfully
[[20:00:28]] [SUCCESS] Screenshot refreshed successfully
[[20:00:28]] [SUCCESS] Screenshot refreshed
[[20:00:28]] [INFO] Refreshing screenshot...
[[20:00:25]] [SUCCESS] Screenshot refreshed successfully
[[20:00:25]] [SUCCESS] Screenshot refreshed successfully
[[20:00:25]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:00:25]] [SUCCESS] Screenshot refreshed
[[20:00:25]] [INFO] Refreshing screenshot...
[[20:00:13]] [SUCCESS] Screenshot refreshed successfully
[[20:00:13]] [SUCCESS] Screenshot refreshed successfully
[[20:00:12]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[20:00:11]] [SUCCESS] Screenshot refreshed
[[20:00:11]] [INFO] Refreshing screenshot...
[[20:00:10]] [SUCCESS] Screenshot refreshed successfully
[[20:00:10]] [SUCCESS] Screenshot refreshed successfully
[[20:00:09]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[20:00:09]] [SUCCESS] Screenshot refreshed
[[20:00:09]] [INFO] Refreshing screenshot...
[[20:00:06]] [SUCCESS] Screenshot refreshed successfully
[[20:00:06]] [SUCCESS] Screenshot refreshed successfully
[[20:00:06]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[20:00:05]] [SUCCESS] Screenshot refreshed
[[20:00:05]] [INFO] Refreshing screenshot...
[[20:00:03]] [SUCCESS] Screenshot refreshed successfully
[[20:00:03]] [SUCCESS] Screenshot refreshed successfully
[[20:00:03]] [INFO] Executing action 7/19: Wait for 1 ms
[[20:00:02]] [SUCCESS] Screenshot refreshed
[[20:00:02]] [INFO] Refreshing screenshot...
[[20:00:01]] [SUCCESS] Screenshot refreshed successfully
[[20:00:01]] [SUCCESS] Screenshot refreshed successfully
[[20:00:00]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[20:00:00]] [SUCCESS] Screenshot refreshed
[[20:00:00]] [INFO] Refreshing screenshot...
[[19:59:57]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:59:57]] [SUCCESS] Screenshot refreshed successfully
[[19:59:57]] [SUCCESS] Screenshot refreshed successfully
[[19:59:56]] [SUCCESS] Screenshot refreshed
[[19:59:56]] [INFO] Refreshing screenshot...
[[19:59:55]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[19:59:55]] [SUCCESS] Screenshot refreshed successfully
[[19:59:55]] [SUCCESS] Screenshot refreshed successfully
[[19:59:54]] [SUCCESS] Screenshot refreshed
[[19:59:54]] [INFO] Refreshing screenshot...
[[19:59:52]] [SUCCESS] Screenshot refreshed successfully
[[19:59:52]] [SUCCESS] Screenshot refreshed successfully
[[19:59:52]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:59:51]] [SUCCESS] Screenshot refreshed
[[19:59:51]] [INFO] Refreshing screenshot...
[[19:59:50]] [SUCCESS] Screenshot refreshed successfully
[[19:59:50]] [SUCCESS] Screenshot refreshed successfully
[[19:59:49]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[19:59:49]] [SUCCESS] Screenshot refreshed
[[19:59:49]] [INFO] Refreshing screenshot...
[[19:59:46]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[19:59:46]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[19:59:46]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[19:59:46]] [INFO] Clearing screenshots from database before execution...
[[19:59:46]] [SUCCESS] All screenshots deleted successfully
[[19:59:46]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[19:59:46]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_195946/screenshots
[[19:59:46]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_195946
[[19:59:46]] [SUCCESS] Report directory initialized successfully
[[19:59:46]] [INFO] Initializing report directory and screenshots folder...
[[19:59:44]] [SUCCESS] All screenshots deleted successfully
[[19:59:44]] [INFO] All actions cleared
[[19:59:44]] [INFO] Cleaning up screenshots...
[[19:59:41]] [SUCCESS] Screenshot refreshed successfully
[[19:59:40]] [SUCCESS] Screenshot refreshed
[[19:59:40]] [INFO] Refreshing screenshot...
[[19:59:39]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[19:59:39]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[19:59:36]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[19:59:35]] [SUCCESS] Found 1 device(s)
[[19:59:34]] [INFO] Refreshing device list...
