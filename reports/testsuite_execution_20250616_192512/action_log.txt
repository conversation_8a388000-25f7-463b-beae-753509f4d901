Action Log - 2025-06-16 19:26:18
================================================================================

[[19:26:18]] [INFO] Generating execution report...
[[19:26:18]] [SUCCESS] All tests passed successfully!
[[19:26:18]] [SUCCESS] Screenshot refreshed
[[19:26:18]] [INFO] Refreshing screenshot...
[[19:26:16]] [SUCCESS] Screenshot refreshed successfully
[[19:26:16]] [SUCCESS] Screenshot refreshed successfully
[[19:26:15]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[19:26:15]] [SUCCESS] Screenshot refreshed
[[19:26:15]] [INFO] Refreshing screenshot...
[[19:26:12]] [SUCCESS] Screenshot refreshed successfully
[[19:26:12]] [SUCCESS] Screenshot refreshed successfully
[[19:26:12]] [INFO] Executing action 18/19: takeScreenshot action
[[19:26:11]] [SUCCESS] Screenshot refreshed
[[19:26:11]] [INFO] Refreshing screenshot...
[[19:26:08]] [SUCCESS] Screenshot refreshed successfully
[[19:26:08]] [SUCCESS] Screenshot refreshed successfully
[[19:26:08]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[19:26:07]] [SUCCESS] Screenshot refreshed
[[19:26:07]] [INFO] Refreshing screenshot...
[[19:26:04]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:26:04]] [SUCCESS] Screenshot refreshed successfully
[[19:26:04]] [SUCCESS] Screenshot refreshed successfully
[[19:26:04]] [SUCCESS] Screenshot refreshed
[[19:26:04]] [INFO] Refreshing screenshot...
[[19:26:02]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[19:26:02]] [SUCCESS] Screenshot refreshed successfully
[[19:26:02]] [SUCCESS] Screenshot refreshed successfully
[[19:26:02]] [SUCCESS] Screenshot refreshed
[[19:26:02]] [INFO] Refreshing screenshot...
[[19:25:59]] [SUCCESS] Screenshot refreshed successfully
[[19:25:59]] [SUCCESS] Screenshot refreshed successfully
[[19:25:59]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:25:59]] [SUCCESS] Screenshot refreshed
[[19:25:59]] [INFO] Refreshing screenshot...
[[19:25:56]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:25:55]] [SUCCESS] Screenshot refreshed successfully
[[19:25:55]] [SUCCESS] Screenshot refreshed successfully
[[19:25:55]] [SUCCESS] Screenshot refreshed
[[19:25:55]] [INFO] Refreshing screenshot...
[[19:25:53]] [INFO] Executing action 12/19: takeScreenshot action
[[19:25:53]] [SUCCESS] Screenshot refreshed successfully
[[19:25:53]] [SUCCESS] Screenshot refreshed successfully
[[19:25:53]] [SUCCESS] Screenshot refreshed
[[19:25:53]] [INFO] Refreshing screenshot...
[[19:25:50]] [SUCCESS] Screenshot refreshed successfully
[[19:25:50]] [SUCCESS] Screenshot refreshed successfully
[[19:25:50]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:25:50]] [SUCCESS] Screenshot refreshed
[[19:25:50]] [INFO] Refreshing screenshot...
[[19:25:37]] [SUCCESS] Screenshot refreshed successfully
[[19:25:37]] [SUCCESS] Screenshot refreshed successfully
[[19:25:37]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[19:25:36]] [SUCCESS] Screenshot refreshed
[[19:25:36]] [INFO] Refreshing screenshot...
[[19:25:35]] [SUCCESS] Screenshot refreshed successfully
[[19:25:35]] [SUCCESS] Screenshot refreshed successfully
[[19:25:34]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[19:25:34]] [SUCCESS] Screenshot refreshed
[[19:25:34]] [INFO] Refreshing screenshot...
[[19:25:31]] [SUCCESS] Screenshot refreshed successfully
[[19:25:31]] [SUCCESS] Screenshot refreshed successfully
[[19:25:30]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[19:25:30]] [SUCCESS] Screenshot refreshed
[[19:25:30]] [INFO] Refreshing screenshot...
[[19:25:28]] [SUCCESS] Screenshot refreshed successfully
[[19:25:28]] [SUCCESS] Screenshot refreshed successfully
[[19:25:27]] [INFO] Executing action 7/19: Wait for 1 ms
[[19:25:27]] [SUCCESS] Screenshot refreshed
[[19:25:27]] [INFO] Refreshing screenshot...
[[19:25:25]] [SUCCESS] Screenshot refreshed successfully
[[19:25:25]] [SUCCESS] Screenshot refreshed successfully
[[19:25:25]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[19:25:25]] [SUCCESS] Screenshot refreshed
[[19:25:25]] [INFO] Refreshing screenshot...
[[19:25:22]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:25:22]] [SUCCESS] Screenshot refreshed successfully
[[19:25:22]] [SUCCESS] Screenshot refreshed successfully
[[19:25:21]] [SUCCESS] Screenshot refreshed
[[19:25:21]] [INFO] Refreshing screenshot...
[[19:25:20]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[19:25:20]] [SUCCESS] Screenshot refreshed successfully
[[19:25:20]] [SUCCESS] Screenshot refreshed successfully
[[19:25:19]] [SUCCESS] Screenshot refreshed
[[19:25:19]] [INFO] Refreshing screenshot...
[[19:25:16]] [SUCCESS] Screenshot refreshed successfully
[[19:25:16]] [SUCCESS] Screenshot refreshed successfully
[[19:25:16]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:25:16]] [SUCCESS] Screenshot refreshed
[[19:25:16]] [INFO] Refreshing screenshot...
[[19:25:14]] [SUCCESS] Screenshot refreshed successfully
[[19:25:14]] [SUCCESS] Screenshot refreshed successfully
[[19:25:14]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[19:25:14]] [SUCCESS] Screenshot refreshed
[[19:25:14]] [INFO] Refreshing screenshot...
[[19:25:12]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[19:25:12]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[19:25:12]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[19:25:12]] [INFO] Clearing screenshots from database before execution...
[[19:25:12]] [SUCCESS] All screenshots deleted successfully
[[19:25:12]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[19:25:12]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_192512/screenshots
[[19:25:12]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_192512
[[19:25:12]] [SUCCESS] Report directory initialized successfully
[[19:25:12]] [INFO] Initializing report directory and screenshots folder...
[[19:25:09]] [SUCCESS] All screenshots deleted successfully
[[19:25:09]] [INFO] All actions cleared
[[19:25:09]] [INFO] Cleaning up screenshots...
[[19:25:05]] [SUCCESS] Screenshot refreshed successfully
[[19:25:04]] [SUCCESS] Screenshot refreshed
[[19:25:04]] [INFO] Refreshing screenshot...
[[19:25:03]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[19:25:03]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[19:25:01]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[19:24:57]] [SUCCESS] Found 1 device(s)
[[19:24:56]] [INFO] Refreshing device list...
