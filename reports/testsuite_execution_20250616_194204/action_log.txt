Action Log - 2025-06-16 19:43:22
================================================================================

[[19:43:21]] [INFO] Generating execution report...
[[19:43:21]] [SUCCESS] All tests passed successfully!
[[19:43:21]] [SUCCESS] Screenshot refreshed
[[19:43:21]] [INFO] Refreshing screenshot...
[[19:43:19]] [SUCCESS] Screenshot refreshed successfully
[[19:43:19]] [SUCCESS] Screenshot refreshed successfully
[[19:43:19]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[19:43:18]] [SUCCESS] Screenshot refreshed
[[19:43:18]] [INFO] Refreshing screenshot...
[[19:43:16]] [SUCCESS] Screenshot refreshed successfully
[[19:43:16]] [SUCCESS] Screenshot refreshed successfully
[[19:43:15]] [INFO] Executing action 18/19: takeScreenshot action
[[19:43:15]] [SUCCESS] Screenshot refreshed
[[19:43:15]] [INFO] Refreshing screenshot...
[[19:43:11]] [SUCCESS] Screenshot refreshed successfully
[[19:43:11]] [SUCCESS] Screenshot refreshed successfully
[[19:43:11]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[19:43:11]] [SUCCESS] Screenshot refreshed
[[19:43:11]] [INFO] Refreshing screenshot...
[[19:43:08]] [SUCCESS] Screenshot refreshed successfully
[[19:43:08]] [SUCCESS] Screenshot refreshed successfully
[[19:43:08]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:43:07]] [SUCCESS] Screenshot refreshed
[[19:43:07]] [INFO] Refreshing screenshot...
[[19:43:06]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[19:43:06]] [SUCCESS] Screenshot refreshed successfully
[[19:43:06]] [SUCCESS] Screenshot refreshed successfully
[[19:43:05]] [SUCCESS] Screenshot refreshed
[[19:43:05]] [INFO] Refreshing screenshot...
[[19:43:03]] [SUCCESS] Screenshot refreshed successfully
[[19:43:03]] [SUCCESS] Screenshot refreshed successfully
[[19:43:03]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:43:02]] [SUCCESS] Screenshot refreshed
[[19:43:02]] [INFO] Refreshing screenshot...
[[19:42:59]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:42:59]] [SUCCESS] Screenshot refreshed successfully
[[19:42:59]] [SUCCESS] Screenshot refreshed successfully
[[19:42:58]] [SUCCESS] Screenshot refreshed
[[19:42:58]] [INFO] Refreshing screenshot...
[[19:42:56]] [INFO] Executing action 12/19: takeScreenshot action
[[19:42:56]] [SUCCESS] Screenshot refreshed successfully
[[19:42:56]] [SUCCESS] Screenshot refreshed successfully
[[19:42:56]] [SUCCESS] Screenshot refreshed
[[19:42:56]] [INFO] Refreshing screenshot...
[[19:42:53]] [SUCCESS] Screenshot refreshed successfully
[[19:42:53]] [SUCCESS] Screenshot refreshed successfully
[[19:42:53]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:42:53]] [SUCCESS] Screenshot refreshed
[[19:42:53]] [INFO] Refreshing screenshot...
[[19:42:41]] [SUCCESS] Screenshot refreshed successfully
[[19:42:41]] [SUCCESS] Screenshot refreshed successfully
[[19:42:40]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[19:42:40]] [SUCCESS] Screenshot refreshed
[[19:42:40]] [INFO] Refreshing screenshot...
[[19:42:37]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[19:42:32]] [SUCCESS] Screenshot refreshed successfully
[[19:42:32]] [SUCCESS] Screenshot refreshed successfully
[[19:42:31]] [SUCCESS] Screenshot refreshed
[[19:42:31]] [INFO] Refreshing screenshot...
[[19:42:28]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[19:42:24]] [SUCCESS] Screenshot refreshed successfully
[[19:42:24]] [SUCCESS] Screenshot refreshed successfully
[[19:42:24]] [SUCCESS] Screenshot refreshed
[[19:42:24]] [INFO] Refreshing screenshot...
[[19:42:21]] [SUCCESS] Screenshot refreshed successfully
[[19:42:21]] [SUCCESS] Screenshot refreshed successfully
[[19:42:21]] [INFO] Executing action 7/19: Wait for 1 ms
[[19:42:21]] [SUCCESS] Screenshot refreshed
[[19:42:21]] [INFO] Refreshing screenshot...
[[19:42:19]] [SUCCESS] Screenshot refreshed successfully
[[19:42:19]] [SUCCESS] Screenshot refreshed successfully
[[19:42:19]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[19:42:18]] [SUCCESS] Screenshot refreshed
[[19:42:18]] [INFO] Refreshing screenshot...
[[19:42:15]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:42:15]] [SUCCESS] Screenshot refreshed successfully
[[19:42:15]] [SUCCESS] Screenshot refreshed successfully
[[19:42:15]] [SUCCESS] Screenshot refreshed
[[19:42:15]] [INFO] Refreshing screenshot...
[[19:42:13]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[19:42:13]] [SUCCESS] Screenshot refreshed successfully
[[19:42:13]] [SUCCESS] Screenshot refreshed successfully
[[19:42:13]] [SUCCESS] Screenshot refreshed
[[19:42:13]] [INFO] Refreshing screenshot...
[[19:42:10]] [SUCCESS] Screenshot refreshed successfully
[[19:42:10]] [SUCCESS] Screenshot refreshed successfully
[[19:42:10]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:42:09]] [SUCCESS] Screenshot refreshed
[[19:42:09]] [INFO] Refreshing screenshot...
[[19:42:08]] [SUCCESS] Screenshot refreshed successfully
[[19:42:08]] [SUCCESS] Screenshot refreshed successfully
[[19:42:08]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[19:42:07]] [SUCCESS] Screenshot refreshed
[[19:42:07]] [INFO] Refreshing screenshot...
[[19:42:04]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[19:42:04]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[19:42:04]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[19:42:04]] [INFO] Clearing screenshots from database before execution...
[[19:42:04]] [SUCCESS] All screenshots deleted successfully
[[19:42:04]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[19:42:04]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_194204/screenshots
[[19:42:04]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_194204
[[19:42:04]] [SUCCESS] Report directory initialized successfully
[[19:42:04]] [INFO] Initializing report directory and screenshots folder...
[[19:42:03]] [SUCCESS] All screenshots deleted successfully
[[19:42:03]] [INFO] All actions cleared
[[19:42:03]] [INFO] Cleaning up screenshots...
[[19:42:00]] [SUCCESS] Screenshot refreshed successfully
[[19:41:59]] [SUCCESS] Screenshot refreshed
[[19:41:59]] [INFO] Refreshing screenshot...
[[19:41:58]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[19:41:58]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[19:41:55]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[19:41:53]] [SUCCESS] Found 1 device(s)
[[19:41:52]] [INFO] Refreshing device list...
