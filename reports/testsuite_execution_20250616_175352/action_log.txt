Action Log - 2025-06-16 17:56:00
================================================================================

[[17:56:00]] [INFO] Generating execution report...
[[17:56:00]] [WARNING] 1 test failed.
[[17:56:00]] [INFO] Skipping remaining steps in failed test case (moving from action 26 to next test case at 27)
[[17:56:00]] [INFO] Moving to the next test case after failure (server will handle retry)
[[17:56:00]] [ERROR] Multi Step action step 3 failed: Screenshot file not found after capture
[[17:55:58]] [INFO] Executing Multi Step action step 3/11: takeScreenshot action
[[17:55:58]] [SUCCESS] Screenshot refreshed successfully
[[17:55:58]] [SUCCESS] Screenshot refreshed successfully
[[17:55:58]] [SUCCESS] Screenshot refreshed
[[17:55:58]] [INFO] Refreshing screenshot...
[[17:55:55]] [SUCCESS] Screenshot refreshed successfully
[[17:55:55]] [SUCCESS] Screenshot refreshed successfully
[[17:55:55]] [INFO] Executing Multi Step action step 2/11: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:55:55]] [SUCCESS] Screenshot refreshed
[[17:55:55]] [INFO] Refreshing screenshot...
[[17:55:50]] [SUCCESS] Screenshot refreshed successfully
[[17:55:50]] [SUCCESS] Screenshot refreshed successfully
[[17:55:50]] [INFO] Executing Multi Step action step 1/11: Launch app: com.apple.Health
[[17:55:50]] [INFO] Loaded 11 steps from test case: apple health
[[17:55:50]] [INFO] Loading steps for Multi Step action: apple health
[[17:55:50]] [INFO] Executing action 26/27: Execute Test Case: apple health (8 steps)
[[17:55:49]] [SUCCESS] Screenshot refreshed
[[17:55:49]] [INFO] Refreshing screenshot...
[[17:55:47]] [SUCCESS] Screenshot refreshed successfully
[[17:55:47]] [SUCCESS] Screenshot refreshed successfully
[[17:55:46]] [INFO] Executing action 25/27: takeScreenshot action
[[17:55:46]] [SUCCESS] Screenshot refreshed
[[17:55:46]] [INFO] Refreshing screenshot...
[[17:55:42]] [SUCCESS] Screenshot refreshed successfully
[[17:55:42]] [SUCCESS] Screenshot refreshed successfully
[[17:55:42]] [INFO] Executing action 24/27: Terminate app: com.apple.Health
[[17:55:42]] [SUCCESS] Screenshot refreshed
[[17:55:42]] [INFO] Refreshing screenshot...
[[17:55:39]] [INFO] Executing action 23/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:55:39]] [SUCCESS] Screenshot refreshed successfully
[[17:55:39]] [SUCCESS] Screenshot refreshed successfully
[[17:55:38]] [SUCCESS] Screenshot refreshed
[[17:55:38]] [INFO] Refreshing screenshot...
[[17:55:37]] [INFO] Executing action 22/27: Add Log: Clicked on Edit link successfully (with screenshot)
[[17:55:37]] [SUCCESS] Screenshot refreshed successfully
[[17:55:37]] [SUCCESS] Screenshot refreshed successfully
[[17:55:36]] [SUCCESS] Screenshot refreshed
[[17:55:36]] [INFO] Refreshing screenshot...
[[17:55:34]] [SUCCESS] Screenshot refreshed successfully
[[17:55:34]] [SUCCESS] Screenshot refreshed successfully
[[17:55:34]] [INFO] Executing action 21/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:55:33]] [SUCCESS] Screenshot refreshed
[[17:55:33]] [INFO] Refreshing screenshot...
[[17:55:30]] [INFO] Executing action 20/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:29]] [SUCCESS] Screenshot refreshed
[[17:55:29]] [INFO] Refreshing screenshot...
[[17:55:27]] [INFO] Executing action 19/27: takeScreenshot action
[[17:55:27]] [SUCCESS] Screenshot refreshed successfully
[[17:55:27]] [SUCCESS] Screenshot refreshed successfully
[[17:55:27]] [SUCCESS] Screenshot refreshed
[[17:55:27]] [INFO] Refreshing screenshot...
[[17:55:24]] [SUCCESS] Screenshot refreshed successfully
[[17:55:24]] [SUCCESS] Screenshot refreshed successfully
[[17:55:24]] [INFO] Executing action 18/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:55:24]] [SUCCESS] Screenshot refreshed
[[17:55:24]] [INFO] Refreshing screenshot...
[[17:55:12]] [SUCCESS] Screenshot refreshed successfully
[[17:55:12]] [SUCCESS] Screenshot refreshed successfully
[[17:55:11]] [INFO] Executing action 17/27: Launch app: com.apple.Health
[[17:55:11]] [SUCCESS] Screenshot refreshed
[[17:55:11]] [INFO] Refreshing screenshot...
[[17:55:11]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:55:11]] [INFO] Executing action 16/27: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[17:55:11]] [SUCCESS] Screenshot refreshed
[[17:55:11]] [INFO] Refreshing screenshot...
[[17:55:10]] [SUCCESS] Screenshot refreshed
[[17:55:10]] [INFO] Refreshing screenshot...
[[17:55:09]] [SUCCESS] Screenshot refreshed successfully
[[17:55:09]] [SUCCESS] Screenshot refreshed successfully
[[17:55:08]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[17:55:07]] [SUCCESS] Screenshot refreshed
[[17:55:07]] [INFO] Refreshing screenshot...
[[17:55:04]] [SUCCESS] Screenshot refreshed successfully
[[17:55:04]] [SUCCESS] Screenshot refreshed successfully
[[17:55:04]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[17:55:04]] [SUCCESS] Screenshot refreshed
[[17:55:04]] [INFO] Refreshing screenshot...
[[17:55:01]] [SUCCESS] Screenshot refreshed successfully
[[17:55:01]] [SUCCESS] Screenshot refreshed successfully
[[17:55:01]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[17:55:01]] [SUCCESS] Screenshot refreshed
[[17:55:01]] [INFO] Refreshing screenshot...
[[17:54:59]] [SUCCESS] Screenshot refreshed successfully
[[17:54:59]] [SUCCESS] Screenshot refreshed successfully
[[17:54:59]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[17:54:59]] [SUCCESS] Screenshot refreshed
[[17:54:59]] [INFO] Refreshing screenshot...
[[17:54:55]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:54:55]] [SUCCESS] Screenshot refreshed successfully
[[17:54:55]] [SUCCESS] Screenshot refreshed successfully
[[17:54:55]] [SUCCESS] Screenshot refreshed
[[17:54:55]] [INFO] Refreshing screenshot...
[[17:54:53]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[17:54:53]] [SUCCESS] Screenshot refreshed successfully
[[17:54:53]] [SUCCESS] Screenshot refreshed successfully
[[17:54:53]] [SUCCESS] Screenshot refreshed
[[17:54:53]] [INFO] Refreshing screenshot...
[[17:54:50]] [SUCCESS] Screenshot refreshed successfully
[[17:54:50]] [SUCCESS] Screenshot refreshed successfully
[[17:54:50]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:54:50]] [SUCCESS] Screenshot refreshed
[[17:54:50]] [INFO] Refreshing screenshot...
[[17:54:48]] [SUCCESS] Screenshot refreshed successfully
[[17:54:48]] [SUCCESS] Screenshot refreshed successfully
[[17:54:48]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[17:54:48]] [SUCCESS] Screenshot refreshed
[[17:54:48]] [INFO] Refreshing screenshot...
[[17:54:44]] [SUCCESS] Screenshot refreshed successfully
[[17:54:44]] [SUCCESS] Screenshot refreshed successfully
[[17:54:43]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[17:54:43]] [INFO] Loaded 9 steps from test case: health2
[[17:54:43]] [INFO] Loading steps for Multi Step action: health2
[[17:54:43]] [INFO] Executing action 15/27: Execute Test Case: health2 (9 steps)
[[17:54:42]] [SUCCESS] Screenshot refreshed
[[17:54:42]] [INFO] Refreshing screenshot...
[[17:54:41]] [SUCCESS] Screenshot refreshed successfully
[[17:54:41]] [SUCCESS] Screenshot refreshed successfully
[[17:54:40]] [INFO] Executing action 14/27: Add Log: Closed App Successfully (with screenshot)
[[17:54:40]] [SUCCESS] Screenshot refreshed
[[17:54:40]] [INFO] Refreshing screenshot...
[[17:54:37]] [SUCCESS] Screenshot refreshed successfully
[[17:54:37]] [SUCCESS] Screenshot refreshed successfully
[[17:54:37]] [INFO] Executing action 13/27: Terminate app: com.apple.Health
[[17:54:36]] [SUCCESS] Screenshot refreshed
[[17:54:36]] [INFO] Refreshing screenshot...
[[17:54:33]] [INFO] Executing action 12/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:54:33]] [SUCCESS] Screenshot refreshed successfully
[[17:54:33]] [SUCCESS] Screenshot refreshed successfully
[[17:54:33]] [SUCCESS] Screenshot refreshed
[[17:54:33]] [INFO] Refreshing screenshot...
[[17:54:30]] [SUCCESS] Screenshot refreshed successfully
[[17:54:30]] [SUCCESS] Screenshot refreshed successfully
[[17:54:30]] [INFO] Executing action 11/27: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[17:54:30]] [SUCCESS] Screenshot refreshed
[[17:54:30]] [INFO] Refreshing screenshot...
[[17:54:18]] [SUCCESS] Screenshot refreshed successfully
[[17:54:18]] [SUCCESS] Screenshot refreshed successfully
[[17:54:17]] [INFO] Executing action 10/27: Launch app: com.apple.Health
[[17:54:16]] [SUCCESS] Screenshot refreshed
[[17:54:16]] [INFO] Refreshing screenshot...
[[17:54:15]] [SUCCESS] Screenshot refreshed successfully
[[17:54:15]] [SUCCESS] Screenshot refreshed successfully
[[17:54:14]] [INFO] Executing action 9/27: Add Log: App is closed (with screenshot)
[[17:54:14]] [SUCCESS] Screenshot refreshed
[[17:54:14]] [INFO] Refreshing screenshot...
[[17:54:11]] [SUCCESS] Screenshot refreshed successfully
[[17:54:11]] [SUCCESS] Screenshot refreshed successfully
[[17:54:11]] [INFO] Executing action 8/27: Terminate app: com.apple.Health
[[17:54:10]] [SUCCESS] Screenshot refreshed
[[17:54:10]] [INFO] Refreshing screenshot...
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [INFO] Executing action 7/27: Wait for 1 ms
[[17:54:07]] [SUCCESS] Screenshot refreshed
[[17:54:07]] [INFO] Refreshing screenshot...
[[17:54:06]] [SUCCESS] Screenshot refreshed successfully
[[17:54:06]] [SUCCESS] Screenshot refreshed successfully
[[17:54:06]] [INFO] Executing action 6/27: Add Log: Done link is clicked (with screenshot)
[[17:54:05]] [SUCCESS] Screenshot refreshed
[[17:54:05]] [INFO] Refreshing screenshot...
[[17:54:02]] [INFO] Executing action 5/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:54:02]] [SUCCESS] Screenshot refreshed successfully
[[17:54:02]] [SUCCESS] Screenshot refreshed successfully
[[17:54:01]] [SUCCESS] Screenshot refreshed
[[17:54:01]] [INFO] Refreshing screenshot...
[[17:54:00]] [INFO] Executing action 4/27: Add Log: Edit link is clicked (with screenshot)
[[17:54:00]] [SUCCESS] Screenshot refreshed successfully
[[17:54:00]] [SUCCESS] Screenshot refreshed successfully
[[17:53:59]] [SUCCESS] Screenshot refreshed
[[17:53:59]] [INFO] Refreshing screenshot...
[[17:53:57]] [SUCCESS] Screenshot refreshed successfully
[[17:53:57]] [SUCCESS] Screenshot refreshed successfully
[[17:53:57]] [INFO] Executing action 3/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:53:56]] [SUCCESS] Screenshot refreshed
[[17:53:56]] [INFO] Refreshing screenshot...
[[17:53:55]] [SUCCESS] Screenshot refreshed successfully
[[17:53:55]] [SUCCESS] Screenshot refreshed successfully
[[17:53:55]] [INFO] Executing action 2/27: Add Log: Launched App Successfully (with screenshot)
[[17:53:54]] [SUCCESS] Screenshot refreshed
[[17:53:54]] [INFO] Refreshing screenshot...
[[17:53:52]] [INFO] Executing action 1/27: Launch app: com.apple.Health
[[17:53:52]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[17:53:52]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[17:53:52]] [INFO] Clearing screenshots from database before execution...
[[17:53:52]] [SUCCESS] All screenshots deleted successfully
[[17:53:52]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:53:52]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_175352/screenshots
[[17:53:52]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_175352
[[17:53:52]] [SUCCESS] Report directory initialized successfully
[[17:53:52]] [INFO] Initializing report directory and screenshots folder...
[[17:53:50]] [SUCCESS] All screenshots deleted successfully
[[17:53:50]] [INFO] All actions cleared
[[17:53:50]] [INFO] Cleaning up screenshots...
[[17:53:44]] [SUCCESS] Screenshot refreshed successfully
[[17:53:43]] [SUCCESS] Screenshot refreshed
[[17:53:43]] [INFO] Refreshing screenshot...
[[17:53:42]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:53:42]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:53:37]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:53:36]] [SUCCESS] Found 1 device(s)
[[17:53:35]] [INFO] Refreshing device list...
