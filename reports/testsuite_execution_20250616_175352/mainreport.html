<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/16/2025, 5:56:00 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 16/06/2025, 17:56:00
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">194ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">12ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1250ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">13ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1670ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">12ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1009ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1059ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">11ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1155ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">866ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1555ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1054ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">13ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="11 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            11 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1227ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1138ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="takeScreen.png" data-action-id="takeScreen" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                takeScreenshot action <span class="action-id-badge" title="Action ID: takeScreen">takeScreen</span>
                            </div>
                            <span class="test-step-duration">456ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1599ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1284ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="successful.png" data-action-id="successful" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: successful">successful</span>
                            </div>
                            <span class="test-step-duration">16ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1575ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1112ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="takeScreen.png" data-action-id="takeScreen" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                takeScreenshot action <span class="action-id-badge" title="Action ID: takeScreen">takeScreen</span>
                            </div>
                            <span class="test-step-duration">1110ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="failed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-2-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-2-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 16/06/2025, 17:56:00","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"194ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"12ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1250ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"13ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1670ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"12ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1009ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1059ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"11ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1155ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"866ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1555ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1054ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"passed","duration":"13ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"passed","duration":"0ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"passed","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            11 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1227ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1138ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"takeScreenshot action","status":"passed","duration":"456ms","action_id":"takeScreen","screenshot_filename":"takeScreen.png","report_screenshot":"takeScreen.png","resolved_screenshot":"screenshots/takeScreen.png","action_id_screenshot":"screenshots/takeScreen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1599ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1284ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"passed","duration":"16ms","action_id":"successful","screenshot_filename":"successful.png","report_screenshot":"successful.png","resolved_screenshot":"screenshots/successful.png","action_id_screenshot":"screenshots/successful.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1575ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1112ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"takeScreenshot action","status":"passed","duration":"1110ms","action_id":"takeScreen","screenshot_filename":"takeScreen.png","report_screenshot":"takeScreen.png","resolved_screenshot":"screenshots/takeScreen.png","action_id_screenshot":"screenshots/takeScreen.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"failed","duration":"0ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: App is closed (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]}],"passed":2,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","7MOUNxtPJz.png","AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","KfOSdvcOkk.png","KzjZOcsLuC.png","OqYf9xF3oX.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","WaakzZc6xF.png","ag29wsBP24.png","ee5KkVz90e.png","f5C7GOVKXJ.png","jE4eZaRFK6.png","jF4jRny1iE.png","mOoxO3pBlm.png","mmT4QEfEZD.png","oIAtyQB5wY.png","rxDTLvtHmR.png","wp1dY1wJ58.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>