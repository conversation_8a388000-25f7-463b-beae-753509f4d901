Action Log - 2025-06-16 19:06:34
================================================================================

[[19:06:34]] [INFO] Generating execution report...
[[19:06:34]] [WARNING] 1 test failed.
[[19:06:34]] [INFO] Skipping remaining steps in failed test case (moving from action 12 to next test case at 19)
[[19:06:34]] [ERROR] Action 12 failed: Screenshot name 'after_edit_link_click' already exists. Please use a unique name.
[[19:06:33]] [INFO] Executing action 12/19: takeScreenshot action
[[19:06:33]] [SUCCESS] Screenshot refreshed successfully
[[19:06:33]] [SUCCESS] Screenshot refreshed successfully
[[19:06:32]] [SUCCESS] Screenshot refreshed
[[19:06:32]] [INFO] Refreshing screenshot...
[[19:06:30]] [SUCCESS] Screenshot refreshed successfully
[[19:06:30]] [SUCCESS] Screenshot refreshed successfully
[[19:06:30]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:06:29]] [SUCCESS] Screenshot refreshed
[[19:06:29]] [INFO] Refreshing screenshot...
[[19:06:17]] [SUCCESS] Screenshot refreshed successfully
[[19:06:17]] [SUCCESS] Screenshot refreshed successfully
[[19:06:16]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[19:06:16]] [SUCCESS] Screenshot refreshed
[[19:06:16]] [INFO] Refreshing screenshot...
[[19:06:14]] [SUCCESS] Screenshot refreshed successfully
[[19:06:14]] [SUCCESS] Screenshot refreshed successfully
[[19:06:14]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[19:06:13]] [SUCCESS] Screenshot refreshed
[[19:06:13]] [INFO] Refreshing screenshot...
[[19:06:10]] [SUCCESS] Screenshot refreshed successfully
[[19:06:10]] [SUCCESS] Screenshot refreshed successfully
[[19:06:10]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[19:06:09]] [SUCCESS] Screenshot refreshed
[[19:06:09]] [INFO] Refreshing screenshot...
[[19:06:07]] [SUCCESS] Screenshot refreshed successfully
[[19:06:07]] [SUCCESS] Screenshot refreshed successfully
[[19:06:07]] [INFO] Executing action 7/19: Wait for 1 ms
[[19:06:06]] [SUCCESS] Screenshot refreshed
[[19:06:06]] [INFO] Refreshing screenshot...
[[19:06:05]] [SUCCESS] Screenshot refreshed successfully
[[19:06:05]] [SUCCESS] Screenshot refreshed successfully
[[19:06:05]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[19:06:04]] [SUCCESS] Screenshot refreshed
[[19:06:04]] [INFO] Refreshing screenshot...
[[19:06:01]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[19:06:01]] [SUCCESS] Screenshot refreshed successfully
[[19:06:01]] [SUCCESS] Screenshot refreshed successfully
[[19:06:01]] [SUCCESS] Screenshot refreshed
[[19:06:01]] [INFO] Refreshing screenshot...
[[19:05:59]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[19:05:59]] [SUCCESS] Screenshot refreshed successfully
[[19:05:59]] [SUCCESS] Screenshot refreshed successfully
[[19:05:59]] [SUCCESS] Screenshot refreshed
[[19:05:59]] [INFO] Refreshing screenshot...
[[19:05:56]] [SUCCESS] Screenshot refreshed successfully
[[19:05:56]] [SUCCESS] Screenshot refreshed successfully
[[19:05:56]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[19:05:56]] [SUCCESS] Screenshot refreshed
[[19:05:56]] [INFO] Refreshing screenshot...
[[19:05:54]] [SUCCESS] Screenshot refreshed successfully
[[19:05:54]] [SUCCESS] Screenshot refreshed successfully
[[19:05:54]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[19:05:53]] [SUCCESS] Screenshot refreshed
[[19:05:53]] [INFO] Refreshing screenshot...
[[19:05:51]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[19:05:51]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[19:05:51]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[19:05:51]] [INFO] Clearing screenshots from database before execution...
[[19:05:51]] [SUCCESS] All screenshots deleted successfully
[[19:05:51]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[19:05:51]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_190551/screenshots
[[19:05:51]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_190551
[[19:05:51]] [SUCCESS] Report directory initialized successfully
[[19:05:51]] [INFO] Initializing report directory and screenshots folder...
[[19:05:48]] [SUCCESS] All screenshots deleted successfully
[[19:05:48]] [INFO] All actions cleared
[[19:05:48]] [INFO] Cleaning up screenshots...
[[19:05:24]] [SUCCESS] Screenshot refreshed successfully
[[19:05:23]] [SUCCESS] Screenshot refreshed
[[19:05:23]] [INFO] Refreshing screenshot...
[[19:05:22]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[19:05:22]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[19:05:20]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[19:05:18]] [SUCCESS] Found 1 device(s)
[[19:05:17]] [INFO] Refreshing device list...
