Action Log - 2025-06-16 17:42:34
================================================================================

[[17:42:34]] [INFO] Generating execution report...
[[17:42:34]] [WARNING] 1 test failed.
[[17:42:34]] [INFO] Skipping remaining steps in failed test case (moving from action 19 to next test case at 27)
[[17:42:34]] [ERROR] Action 19 failed: Unknown action type: takeScreenshot
[[17:42:32]] [INFO] Executing action 19/27: takeScreenshot action
[[17:42:32]] [SUCCESS] Screenshot refreshed successfully
[[17:42:32]] [SUCCESS] Screenshot refreshed successfully
[[17:42:32]] [SUCCESS] Screenshot refreshed
[[17:42:32]] [INFO] Refreshing screenshot...
[[17:42:29]] [INFO] Executing action 18/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:42:29]] [SUCCESS] Screenshot refreshed successfully
[[17:42:29]] [SUCCESS] Screenshot refreshed successfully
[[17:42:29]] [SUCCESS] Screenshot refreshed
[[17:42:29]] [INFO] Refreshing screenshot...
[[17:42:17]] [SUCCESS] Screenshot refreshed successfully
[[17:42:17]] [SUCCESS] Screenshot refreshed successfully
[[17:42:16]] [INFO] Executing action 17/27: Launch app: com.apple.Health
[[17:42:16]] [SUCCESS] Screenshot refreshed
[[17:42:16]] [INFO] Refreshing screenshot...
[[17:42:16]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:42:16]] [INFO] Executing action 16/27: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[17:42:16]] [SUCCESS] Screenshot refreshed
[[17:42:16]] [INFO] Refreshing screenshot...
[[17:42:15]] [SUCCESS] Screenshot refreshed
[[17:42:15]] [INFO] Refreshing screenshot...
[[17:42:14]] [SUCCESS] Screenshot refreshed successfully
[[17:42:14]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[17:42:12]] [SUCCESS] Screenshot refreshed
[[17:42:12]] [INFO] Refreshing screenshot...
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[17:42:09]] [SUCCESS] Screenshot refreshed
[[17:42:09]] [INFO] Refreshing screenshot...
[[17:42:06]] [SUCCESS] Screenshot refreshed successfully
[[17:42:06]] [SUCCESS] Screenshot refreshed successfully
[[17:42:06]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[17:42:06]] [SUCCESS] Screenshot refreshed
[[17:42:06]] [INFO] Refreshing screenshot...
[[17:42:04]] [SUCCESS] Screenshot refreshed successfully
[[17:42:04]] [SUCCESS] Screenshot refreshed successfully
[[17:42:04]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[17:42:04]] [SUCCESS] Screenshot refreshed
[[17:42:04]] [INFO] Refreshing screenshot...
[[17:42:00]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:42:00]] [SUCCESS] Screenshot refreshed successfully
[[17:42:00]] [SUCCESS] Screenshot refreshed successfully
[[17:42:00]] [SUCCESS] Screenshot refreshed
[[17:42:00]] [INFO] Refreshing screenshot...
[[17:41:59]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[17:41:58]] [SUCCESS] Screenshot refreshed successfully
[[17:41:58]] [SUCCESS] Screenshot refreshed successfully
[[17:41:58]] [SUCCESS] Screenshot refreshed
[[17:41:58]] [INFO] Refreshing screenshot...
[[17:41:55]] [SUCCESS] Screenshot refreshed successfully
[[17:41:55]] [SUCCESS] Screenshot refreshed successfully
[[17:41:55]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:41:55]] [SUCCESS] Screenshot refreshed
[[17:41:55]] [INFO] Refreshing screenshot...
[[17:41:53]] [SUCCESS] Screenshot refreshed successfully
[[17:41:53]] [SUCCESS] Screenshot refreshed successfully
[[17:41:53]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[17:41:53]] [SUCCESS] Screenshot refreshed
[[17:41:53]] [INFO] Refreshing screenshot...
[[17:41:49]] [SUCCESS] Screenshot refreshed successfully
[[17:41:49]] [SUCCESS] Screenshot refreshed successfully
[[17:41:48]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[17:41:48]] [INFO] Loaded 9 steps from test case: health2
[[17:41:48]] [INFO] Loading steps for Multi Step action: health2
[[17:41:48]] [INFO] Executing action 15/27: Execute Test Case: health2 (9 steps)
[[17:41:48]] [SUCCESS] Screenshot refreshed
[[17:41:48]] [INFO] Refreshing screenshot...
[[17:41:46]] [SUCCESS] Screenshot refreshed successfully
[[17:41:46]] [SUCCESS] Screenshot refreshed successfully
[[17:41:45]] [INFO] Executing action 14/27: Add Log: Closed App Successfully (with screenshot)
[[17:41:45]] [SUCCESS] Screenshot refreshed
[[17:41:45]] [INFO] Refreshing screenshot...
[[17:41:42]] [SUCCESS] Screenshot refreshed successfully
[[17:41:42]] [SUCCESS] Screenshot refreshed successfully
[[17:41:42]] [INFO] Executing action 13/27: Terminate app: com.apple.Health
[[17:41:41]] [SUCCESS] Screenshot refreshed
[[17:41:41]] [INFO] Refreshing screenshot...
[[17:41:38]] [INFO] Executing action 12/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:41:38]] [SUCCESS] Screenshot refreshed successfully
[[17:41:38]] [SUCCESS] Screenshot refreshed successfully
[[17:41:38]] [SUCCESS] Screenshot refreshed
[[17:41:38]] [INFO] Refreshing screenshot...
[[17:41:35]] [SUCCESS] Screenshot refreshed successfully
[[17:41:35]] [SUCCESS] Screenshot refreshed successfully
[[17:41:35]] [INFO] Executing action 11/27: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[17:41:35]] [SUCCESS] Screenshot refreshed
[[17:41:35]] [INFO] Refreshing screenshot...
[[17:41:23]] [SUCCESS] Screenshot refreshed successfully
[[17:41:23]] [SUCCESS] Screenshot refreshed successfully
[[17:41:22]] [INFO] Executing action 10/27: Launch app: com.apple.Health
[[17:41:22]] [SUCCESS] Screenshot refreshed
[[17:41:22]] [INFO] Refreshing screenshot...
[[17:41:20]] [SUCCESS] Screenshot refreshed successfully
[[17:41:20]] [SUCCESS] Screenshot refreshed successfully
[[17:41:19]] [INFO] Executing action 9/27: Add Log: App is closed (with screenshot)
[[17:41:19]] [SUCCESS] Screenshot refreshed
[[17:41:19]] [INFO] Refreshing screenshot...
[[17:41:16]] [SUCCESS] Screenshot refreshed successfully
[[17:41:16]] [SUCCESS] Screenshot refreshed successfully
[[17:41:16]] [INFO] Executing action 8/27: Terminate app: com.apple.Health
[[17:41:15]] [SUCCESS] Screenshot refreshed
[[17:41:15]] [INFO] Refreshing screenshot...
[[17:41:13]] [SUCCESS] Screenshot refreshed successfully
[[17:41:13]] [SUCCESS] Screenshot refreshed successfully
[[17:41:13]] [INFO] Executing action 7/27: Wait for 1 ms
[[17:41:12]] [SUCCESS] Screenshot refreshed
[[17:41:12]] [INFO] Refreshing screenshot...
[[17:41:11]] [SUCCESS] Screenshot refreshed successfully
[[17:41:11]] [SUCCESS] Screenshot refreshed successfully
[[17:41:11]] [INFO] Executing action 6/27: Add Log: Done link is clicked (with screenshot)
[[17:41:10]] [SUCCESS] Screenshot refreshed
[[17:41:10]] [INFO] Refreshing screenshot...
[[17:41:07]] [INFO] Executing action 5/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:41:07]] [SUCCESS] Screenshot refreshed successfully
[[17:41:07]] [SUCCESS] Screenshot refreshed successfully
[[17:41:06]] [SUCCESS] Screenshot refreshed
[[17:41:06]] [INFO] Refreshing screenshot...
[[17:41:05]] [INFO] Executing action 4/27: Add Log: Edit link is clicked (with screenshot)
[[17:41:05]] [SUCCESS] Screenshot refreshed successfully
[[17:41:05]] [SUCCESS] Screenshot refreshed successfully
[[17:41:04]] [SUCCESS] Screenshot refreshed
[[17:41:04]] [INFO] Refreshing screenshot...
[[17:41:02]] [SUCCESS] Screenshot refreshed successfully
[[17:41:02]] [SUCCESS] Screenshot refreshed successfully
[[17:41:02]] [INFO] Executing action 3/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:41:01]] [SUCCESS] Screenshot refreshed
[[17:41:01]] [INFO] Refreshing screenshot...
[[17:41:00]] [SUCCESS] Screenshot refreshed successfully
[[17:41:00]] [SUCCESS] Screenshot refreshed successfully
[[17:41:00]] [INFO] Executing action 2/27: Add Log: Launched App Successfully (with screenshot)
[[17:40:59]] [SUCCESS] Screenshot refreshed
[[17:40:59]] [INFO] Refreshing screenshot...
[[17:40:57]] [INFO] Executing action 1/27: Launch app: com.apple.Health
[[17:40:57]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[17:40:57]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[17:40:57]] [INFO] Clearing screenshots from database before execution...
[[17:40:57]] [SUCCESS] All screenshots deleted successfully
[[17:40:57]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:40:57]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_174057/screenshots
[[17:40:57]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_174057
[[17:40:57]] [SUCCESS] Report directory initialized successfully
[[17:40:57]] [INFO] Initializing report directory and screenshots folder...
[[17:40:55]] [SUCCESS] All screenshots deleted successfully
[[17:40:55]] [INFO] All actions cleared
[[17:40:55]] [INFO] Cleaning up screenshots...
[[17:40:49]] [SUCCESS] Screenshot refreshed successfully
[[17:40:48]] [SUCCESS] Screenshot refreshed
[[17:40:48]] [INFO] Refreshing screenshot...
[[17:40:47]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:40:47]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:40:42]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:40:41]] [SUCCESS] Found 1 device(s)
[[17:40:40]] [INFO] Refreshing device list...
