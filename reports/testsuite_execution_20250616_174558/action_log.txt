Action Log - 2025-06-16 17:47:37
================================================================================

[[17:47:37]] [INFO] Generating execution report...
[[17:47:37]] [WARNING] 1 test failed.
[[17:47:37]] [INFO] Skipping remaining steps in failed test case (moving from action 19 to next test case at 27)
[[17:47:37]] [ERROR] Action 19 failed: Screenshot action failed: expected str, bytes or os.PathLike object, not dict
[[17:47:35]] [INFO] Executing action 19/27: takeScreenshot action
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:34]] [SUCCESS] Screenshot refreshed
[[17:47:34]] [INFO] Refreshing screenshot...
[[17:47:32]] [SUCCESS] Screenshot refreshed successfully
[[17:47:32]] [SUCCESS] Screenshot refreshed successfully
[[17:47:32]] [INFO] Executing action 18/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:47:31]] [SUCCESS] Screenshot refreshed
[[17:47:31]] [INFO] Refreshing screenshot...
[[17:47:19]] [SUCCESS] Screenshot refreshed successfully
[[17:47:19]] [SUCCESS] Screenshot refreshed successfully
[[17:47:19]] [INFO] Executing action 17/27: Launch app: com.apple.Health
[[17:47:18]] [SUCCESS] Screenshot refreshed
[[17:47:18]] [INFO] Refreshing screenshot...
[[17:47:18]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:47:18]] [INFO] Executing action 16/27: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[17:47:18]] [SUCCESS] Screenshot refreshed
[[17:47:18]] [INFO] Refreshing screenshot...
[[17:47:18]] [SUCCESS] Screenshot refreshed
[[17:47:18]] [INFO] Refreshing screenshot...
[[17:47:16]] [SUCCESS] Screenshot refreshed successfully
[[17:47:16]] [SUCCESS] Screenshot refreshed successfully
[[17:47:15]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[17:47:15]] [SUCCESS] Screenshot refreshed
[[17:47:15]] [INFO] Refreshing screenshot...
[[17:47:12]] [SUCCESS] Screenshot refreshed successfully
[[17:47:12]] [SUCCESS] Screenshot refreshed successfully
[[17:47:12]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[17:47:11]] [SUCCESS] Screenshot refreshed
[[17:47:11]] [INFO] Refreshing screenshot...
[[17:47:09]] [SUCCESS] Screenshot refreshed successfully
[[17:47:09]] [SUCCESS] Screenshot refreshed successfully
[[17:47:09]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[17:47:08]] [SUCCESS] Screenshot refreshed
[[17:47:08]] [INFO] Refreshing screenshot...
[[17:47:07]] [SUCCESS] Screenshot refreshed successfully
[[17:47:07]] [SUCCESS] Screenshot refreshed successfully
[[17:47:07]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[17:47:06]] [SUCCESS] Screenshot refreshed
[[17:47:06]] [INFO] Refreshing screenshot...
[[17:47:03]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:47:03]] [SUCCESS] Screenshot refreshed successfully
[[17:47:03]] [SUCCESS] Screenshot refreshed successfully
[[17:47:02]] [SUCCESS] Screenshot refreshed
[[17:47:02]] [INFO] Refreshing screenshot...
[[17:47:01]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[17:47:01]] [SUCCESS] Screenshot refreshed successfully
[[17:47:01]] [SUCCESS] Screenshot refreshed successfully
[[17:47:00]] [SUCCESS] Screenshot refreshed
[[17:47:00]] [INFO] Refreshing screenshot...
[[17:46:58]] [SUCCESS] Screenshot refreshed successfully
[[17:46:58]] [SUCCESS] Screenshot refreshed successfully
[[17:46:58]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:46:57]] [SUCCESS] Screenshot refreshed
[[17:46:57]] [INFO] Refreshing screenshot...
[[17:46:56]] [SUCCESS] Screenshot refreshed successfully
[[17:46:56]] [SUCCESS] Screenshot refreshed successfully
[[17:46:56]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[17:46:55]] [SUCCESS] Screenshot refreshed
[[17:46:55]] [INFO] Refreshing screenshot...
[[17:46:51]] [SUCCESS] Screenshot refreshed successfully
[[17:46:51]] [SUCCESS] Screenshot refreshed successfully
[[17:46:50]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[17:46:50]] [INFO] Loaded 9 steps from test case: health2
[[17:46:50]] [INFO] Loading steps for Multi Step action: health2
[[17:46:50]] [INFO] Executing action 15/27: Execute Test Case: health2 (9 steps)
[[17:46:50]] [SUCCESS] Screenshot refreshed
[[17:46:50]] [INFO] Refreshing screenshot...
[[17:46:48]] [SUCCESS] Screenshot refreshed successfully
[[17:46:48]] [SUCCESS] Screenshot refreshed successfully
[[17:46:48]] [INFO] Executing action 14/27: Add Log: Closed App Successfully (with screenshot)
[[17:46:47]] [SUCCESS] Screenshot refreshed
[[17:46:47]] [INFO] Refreshing screenshot...
[[17:46:44]] [SUCCESS] Screenshot refreshed successfully
[[17:46:44]] [SUCCESS] Screenshot refreshed successfully
[[17:46:44]] [INFO] Executing action 13/27: Terminate app: com.apple.Health
[[17:46:44]] [SUCCESS] Screenshot refreshed
[[17:46:44]] [INFO] Refreshing screenshot...
[[17:46:40]] [INFO] Executing action 12/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:46:40]] [SUCCESS] Screenshot refreshed successfully
[[17:46:40]] [SUCCESS] Screenshot refreshed successfully
[[17:46:40]] [SUCCESS] Screenshot refreshed
[[17:46:40]] [INFO] Refreshing screenshot...
[[17:46:38]] [SUCCESS] Screenshot refreshed successfully
[[17:46:38]] [SUCCESS] Screenshot refreshed successfully
[[17:46:38]] [INFO] Executing action 11/27: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[17:46:37]] [SUCCESS] Screenshot refreshed
[[17:46:37]] [INFO] Refreshing screenshot...
[[17:46:25]] [SUCCESS] Screenshot refreshed successfully
[[17:46:25]] [SUCCESS] Screenshot refreshed successfully
[[17:46:24]] [INFO] Executing action 10/27: Launch app: com.apple.Health
[[17:46:24]] [SUCCESS] Screenshot refreshed
[[17:46:24]] [INFO] Refreshing screenshot...
[[17:46:22]] [SUCCESS] Screenshot refreshed successfully
[[17:46:22]] [SUCCESS] Screenshot refreshed successfully
[[17:46:22]] [INFO] Executing action 9/27: Add Log: App is closed (with screenshot)
[[17:46:21]] [SUCCESS] Screenshot refreshed
[[17:46:21]] [INFO] Refreshing screenshot...
[[17:46:18]] [SUCCESS] Screenshot refreshed successfully
[[17:46:18]] [SUCCESS] Screenshot refreshed successfully
[[17:46:18]] [INFO] Executing action 8/27: Terminate app: com.apple.Health
[[17:46:17]] [SUCCESS] Screenshot refreshed
[[17:46:17]] [INFO] Refreshing screenshot...
[[17:46:15]] [SUCCESS] Screenshot refreshed successfully
[[17:46:15]] [SUCCESS] Screenshot refreshed successfully
[[17:46:14]] [INFO] Executing action 7/27: Wait for 1 ms
[[17:46:14]] [SUCCESS] Screenshot refreshed
[[17:46:14]] [INFO] Refreshing screenshot...
[[17:46:12]] [SUCCESS] Screenshot refreshed successfully
[[17:46:12]] [SUCCESS] Screenshot refreshed successfully
[[17:46:12]] [INFO] Executing action 6/27: Add Log: Done link is clicked (with screenshot)
[[17:46:12]] [SUCCESS] Screenshot refreshed
[[17:46:12]] [INFO] Refreshing screenshot...
[[17:46:08]] [INFO] Executing action 5/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[17:46:08]] [SUCCESS] Screenshot refreshed successfully
[[17:46:08]] [SUCCESS] Screenshot refreshed successfully
[[17:46:08]] [SUCCESS] Screenshot refreshed
[[17:46:08]] [INFO] Refreshing screenshot...
[[17:46:06]] [INFO] Executing action 4/27: Add Log: Edit link is clicked (with screenshot)
[[17:46:06]] [SUCCESS] Screenshot refreshed successfully
[[17:46:06]] [SUCCESS] Screenshot refreshed successfully
[[17:46:06]] [SUCCESS] Screenshot refreshed
[[17:46:06]] [INFO] Refreshing screenshot...
[[17:46:03]] [SUCCESS] Screenshot refreshed successfully
[[17:46:03]] [SUCCESS] Screenshot refreshed successfully
[[17:46:03]] [INFO] Executing action 3/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[17:46:02]] [SUCCESS] Screenshot refreshed
[[17:46:02]] [INFO] Refreshing screenshot...
[[17:46:01]] [SUCCESS] Screenshot refreshed successfully
[[17:46:01]] [SUCCESS] Screenshot refreshed successfully
[[17:46:01]] [INFO] Executing action 2/27: Add Log: Launched App Successfully (with screenshot)
[[17:46:00]] [SUCCESS] Screenshot refreshed
[[17:46:00]] [INFO] Refreshing screenshot...
[[17:45:58]] [INFO] Executing action 1/27: Launch app: com.apple.Health
[[17:45:58]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[17:45:58]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[17:45:58]] [INFO] Clearing screenshots from database before execution...
[[17:45:58]] [SUCCESS] All screenshots deleted successfully
[[17:45:58]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:45:58]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_174558/screenshots
[[17:45:58]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_174558
[[17:45:58]] [SUCCESS] Report directory initialized successfully
[[17:45:58]] [INFO] Initializing report directory and screenshots folder...
[[17:45:56]] [SUCCESS] All screenshots deleted successfully
[[17:45:56]] [INFO] All actions cleared
[[17:45:56]] [INFO] Cleaning up screenshots...
[[17:45:40]] [SUCCESS] Screenshot refreshed successfully
[[17:45:38]] [SUCCESS] Screenshot refreshed
[[17:45:38]] [INFO] Refreshing screenshot...
[[17:45:37]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:45:37]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:45:31]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:45:29]] [SUCCESS] Found 1 device(s)
[[17:45:29]] [INFO] Refreshing device list...
