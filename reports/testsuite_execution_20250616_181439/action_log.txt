Action Log - 2025-06-16 18:16:47
================================================================================

[[18:16:47]] [INFO] Generating execution report...
[[18:16:47]] [WARNING] 1 test failed.
[[18:16:47]] [INFO] Skipping remaining steps in failed test case (moving from action 26 to next test case at 27)
[[18:16:47]] [INFO] Moving to the next test case after failure (server will handle retry)
[[18:16:47]] [ERROR] Multi Step action step 3 failed: Screenshot name 'after_edit_link_click' already exists. Please use a unique name.
[[18:16:45]] [INFO] Executing Multi Step action step 3/11: takeScreenshot action
[[18:16:45]] [SUCCESS] Screenshot refreshed successfully
[[18:16:45]] [SUCCESS] Screenshot refreshed successfully
[[18:16:45]] [SUCCESS] Screenshot refreshed
[[18:16:45]] [INFO] Refreshing screenshot...
[[18:16:42]] [SUCCESS] Screenshot refreshed successfully
[[18:16:42]] [SUCCESS] Screenshot refreshed successfully
[[18:16:42]] [INFO] Executing Multi Step action step 2/11: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:16:42]] [SUCCESS] Screenshot refreshed
[[18:16:42]] [INFO] Refreshing screenshot...
[[18:16:38]] [SUCCESS] Screenshot refreshed successfully
[[18:16:38]] [SUCCESS] Screenshot refreshed successfully
[[18:16:37]] [INFO] Executing Multi Step action step 1/11: Launch app: com.apple.Health
[[18:16:37]] [INFO] Loaded 11 steps from test case: apple health
[[18:16:37]] [INFO] Loading steps for Multi Step action: apple health
[[18:16:37]] [INFO] Executing action 26/27: Execute Test Case: apple health (8 steps)
[[18:16:37]] [SUCCESS] Screenshot refreshed
[[18:16:37]] [INFO] Refreshing screenshot...
[[18:16:34]] [SUCCESS] Screenshot refreshed successfully
[[18:16:34]] [SUCCESS] Screenshot refreshed successfully
[[18:16:33]] [INFO] Executing action 25/27: takeScreenshot action
[[18:16:33]] [SUCCESS] Screenshot refreshed
[[18:16:33]] [INFO] Refreshing screenshot...
[[18:16:30]] [SUCCESS] Screenshot refreshed successfully
[[18:16:30]] [SUCCESS] Screenshot refreshed successfully
[[18:16:30]] [INFO] Executing action 24/27: Terminate app: com.apple.Health
[[18:16:29]] [SUCCESS] Screenshot refreshed
[[18:16:29]] [INFO] Refreshing screenshot...
[[18:16:26]] [INFO] Executing action 23/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:25]] [SUCCESS] Screenshot refreshed
[[18:16:25]] [INFO] Refreshing screenshot...
[[18:16:24]] [INFO] Executing action 22/27: Add Log: Clicked on Edit link successfully (with screenshot)
[[18:16:24]] [SUCCESS] Screenshot refreshed successfully
[[18:16:24]] [SUCCESS] Screenshot refreshed successfully
[[18:16:24]] [SUCCESS] Screenshot refreshed
[[18:16:24]] [INFO] Refreshing screenshot...
[[18:16:21]] [SUCCESS] Screenshot refreshed successfully
[[18:16:21]] [SUCCESS] Screenshot refreshed successfully
[[18:16:21]] [INFO] Executing action 21/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:16:20]] [SUCCESS] Screenshot refreshed
[[18:16:20]] [INFO] Refreshing screenshot...
[[18:16:17]] [INFO] Executing action 20/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:16:17]] [SUCCESS] Screenshot refreshed successfully
[[18:16:17]] [SUCCESS] Screenshot refreshed successfully
[[18:16:17]] [SUCCESS] Screenshot refreshed
[[18:16:17]] [INFO] Refreshing screenshot...
[[18:16:15]] [INFO] Executing action 19/27: takeScreenshot action
[[18:16:15]] [SUCCESS] Screenshot refreshed successfully
[[18:16:15]] [SUCCESS] Screenshot refreshed successfully
[[18:16:14]] [SUCCESS] Screenshot refreshed
[[18:16:14]] [INFO] Refreshing screenshot...
[[18:16:12]] [SUCCESS] Screenshot refreshed successfully
[[18:16:12]] [SUCCESS] Screenshot refreshed successfully
[[18:16:12]] [INFO] Executing action 18/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:16:11]] [SUCCESS] Screenshot refreshed
[[18:16:11]] [INFO] Refreshing screenshot...
[[18:15:59]] [SUCCESS] Screenshot refreshed successfully
[[18:15:59]] [SUCCESS] Screenshot refreshed successfully
[[18:15:58]] [INFO] Executing action 17/27: Launch app: com.apple.Health
[[18:15:58]] [SUCCESS] Screenshot refreshed
[[18:15:58]] [INFO] Refreshing screenshot...
[[18:15:58]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[18:15:58]] [INFO] Executing action 16/27: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[18:15:58]] [SUCCESS] Screenshot refreshed
[[18:15:58]] [INFO] Refreshing screenshot...
[[18:15:57]] [SUCCESS] Screenshot refreshed
[[18:15:57]] [INFO] Refreshing screenshot...
[[18:15:56]] [SUCCESS] Screenshot refreshed successfully
[[18:15:56]] [SUCCESS] Screenshot refreshed successfully
[[18:15:55]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[18:15:55]] [SUCCESS] Screenshot refreshed
[[18:15:55]] [INFO] Refreshing screenshot...
[[18:15:52]] [SUCCESS] Screenshot refreshed successfully
[[18:15:52]] [SUCCESS] Screenshot refreshed successfully
[[18:15:52]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[18:15:51]] [SUCCESS] Screenshot refreshed
[[18:15:51]] [INFO] Refreshing screenshot...
[[18:15:49]] [SUCCESS] Screenshot refreshed successfully
[[18:15:49]] [SUCCESS] Screenshot refreshed successfully
[[18:15:48]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[18:15:48]] [SUCCESS] Screenshot refreshed
[[18:15:48]] [INFO] Refreshing screenshot...
[[18:15:46]] [SUCCESS] Screenshot refreshed successfully
[[18:15:46]] [SUCCESS] Screenshot refreshed successfully
[[18:15:46]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[18:15:46]] [SUCCESS] Screenshot refreshed
[[18:15:46]] [INFO] Refreshing screenshot...
[[18:15:43]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:42]] [SUCCESS] Screenshot refreshed
[[18:15:42]] [INFO] Refreshing screenshot...
[[18:15:41]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[18:15:41]] [SUCCESS] Screenshot refreshed successfully
[[18:15:41]] [SUCCESS] Screenshot refreshed successfully
[[18:15:40]] [SUCCESS] Screenshot refreshed
[[18:15:40]] [INFO] Refreshing screenshot...
[[18:15:38]] [SUCCESS] Screenshot refreshed successfully
[[18:15:38]] [SUCCESS] Screenshot refreshed successfully
[[18:15:38]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:15:37]] [SUCCESS] Screenshot refreshed
[[18:15:37]] [INFO] Refreshing screenshot...
[[18:15:36]] [SUCCESS] Screenshot refreshed successfully
[[18:15:36]] [SUCCESS] Screenshot refreshed successfully
[[18:15:36]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[18:15:35]] [SUCCESS] Screenshot refreshed
[[18:15:35]] [INFO] Refreshing screenshot...
[[18:15:31]] [SUCCESS] Screenshot refreshed successfully
[[18:15:31]] [SUCCESS] Screenshot refreshed successfully
[[18:15:30]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[18:15:30]] [INFO] Loaded 9 steps from test case: health2
[[18:15:30]] [INFO] Loading steps for Multi Step action: health2
[[18:15:30]] [INFO] Executing action 15/27: Execute Test Case: health2 (9 steps)
[[18:15:30]] [SUCCESS] Screenshot refreshed
[[18:15:30]] [INFO] Refreshing screenshot...
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [INFO] Executing action 14/27: Add Log: Closed App Successfully (with screenshot)
[[18:15:27]] [SUCCESS] Screenshot refreshed
[[18:15:27]] [INFO] Refreshing screenshot...
[[18:15:24]] [SUCCESS] Screenshot refreshed successfully
[[18:15:24]] [SUCCESS] Screenshot refreshed successfully
[[18:15:24]] [INFO] Executing action 13/27: Terminate app: com.apple.Health
[[18:15:23]] [SUCCESS] Screenshot refreshed
[[18:15:23]] [INFO] Refreshing screenshot...
[[18:15:20]] [INFO] Executing action 12/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:15:20]] [SUCCESS] Screenshot refreshed successfully
[[18:15:20]] [SUCCESS] Screenshot refreshed successfully
[[18:15:20]] [SUCCESS] Screenshot refreshed
[[18:15:20]] [INFO] Refreshing screenshot...
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:18]] [INFO] Executing action 11/27: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[18:15:17]] [SUCCESS] Screenshot refreshed
[[18:15:17]] [INFO] Refreshing screenshot...
[[18:15:05]] [SUCCESS] Screenshot refreshed successfully
[[18:15:05]] [SUCCESS] Screenshot refreshed successfully
[[18:15:04]] [INFO] Executing action 10/27: Launch app: com.apple.Health
[[18:15:04]] [SUCCESS] Screenshot refreshed
[[18:15:04]] [INFO] Refreshing screenshot...
[[18:15:02]] [SUCCESS] Screenshot refreshed successfully
[[18:15:02]] [SUCCESS] Screenshot refreshed successfully
[[18:15:02]] [INFO] Executing action 9/27: Add Log: App is closed (with screenshot)
[[18:15:01]] [SUCCESS] Screenshot refreshed
[[18:15:01]] [INFO] Refreshing screenshot...
[[18:14:58]] [SUCCESS] Screenshot refreshed successfully
[[18:14:58]] [SUCCESS] Screenshot refreshed successfully
[[18:14:58]] [INFO] Executing action 8/27: Terminate app: com.apple.Health
[[18:14:57]] [SUCCESS] Screenshot refreshed
[[18:14:57]] [INFO] Refreshing screenshot...
[[18:14:55]] [SUCCESS] Screenshot refreshed successfully
[[18:14:55]] [SUCCESS] Screenshot refreshed successfully
[[18:14:55]] [INFO] Executing action 7/27: Wait for 1 ms
[[18:14:54]] [SUCCESS] Screenshot refreshed
[[18:14:54]] [INFO] Refreshing screenshot...
[[18:14:53]] [SUCCESS] Screenshot refreshed successfully
[[18:14:53]] [SUCCESS] Screenshot refreshed successfully
[[18:14:53]] [INFO] Executing action 6/27: Add Log: Done link is clicked (with screenshot)
[[18:14:52]] [SUCCESS] Screenshot refreshed
[[18:14:52]] [INFO] Refreshing screenshot...
[[18:14:49]] [INFO] Executing action 5/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:14:49]] [SUCCESS] Screenshot refreshed successfully
[[18:14:49]] [SUCCESS] Screenshot refreshed successfully
[[18:14:49]] [SUCCESS] Screenshot refreshed
[[18:14:49]] [INFO] Refreshing screenshot...
[[18:14:47]] [INFO] Executing action 4/27: Add Log: Edit link is clicked (with screenshot)
[[18:14:47]] [SUCCESS] Screenshot refreshed successfully
[[18:14:47]] [SUCCESS] Screenshot refreshed successfully
[[18:14:47]] [SUCCESS] Screenshot refreshed
[[18:14:47]] [INFO] Refreshing screenshot...
[[18:14:44]] [SUCCESS] Screenshot refreshed successfully
[[18:14:44]] [SUCCESS] Screenshot refreshed successfully
[[18:14:44]] [INFO] Executing action 3/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:14:43]] [SUCCESS] Screenshot refreshed
[[18:14:43]] [INFO] Refreshing screenshot...
[[18:14:42]] [SUCCESS] Screenshot refreshed successfully
[[18:14:42]] [SUCCESS] Screenshot refreshed successfully
[[18:14:42]] [INFO] Executing action 2/27: Add Log: Launched App Successfully (with screenshot)
[[18:14:41]] [SUCCESS] Screenshot refreshed
[[18:14:41]] [INFO] Refreshing screenshot...
[[18:14:39]] [INFO] Executing action 1/27: Launch app: com.apple.Health
[[18:14:39]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[18:14:39]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[18:14:39]] [INFO] Clearing screenshots from database before execution...
[[18:14:39]] [SUCCESS] All screenshots deleted successfully
[[18:14:39]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:14:39]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_181439/screenshots
[[18:14:39]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_181439
[[18:14:39]] [SUCCESS] Report directory initialized successfully
[[18:14:39]] [INFO] Initializing report directory and screenshots folder...
[[18:14:36]] [SUCCESS] All screenshots deleted successfully
[[18:14:36]] [INFO] All actions cleared
[[18:14:36]] [INFO] Cleaning up screenshots...
[[18:14:33]] [SUCCESS] Screenshot refreshed successfully
[[18:14:32]] [SUCCESS] Screenshot refreshed
[[18:14:32]] [INFO] Refreshing screenshot...
[[18:14:31]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:14:31]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:14:26]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:14:03]] [SUCCESS] Found 1 device(s)
[[18:14:02]] [INFO] Refreshing device list...
