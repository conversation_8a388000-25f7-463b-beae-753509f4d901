{"name": "UI Execution 16/06/2025, 19:59:27", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "failed", "duration": "0ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Wait for 1 ms", "status": "unknown", "duration": "0ms", "action_id": "ee5KkVz90e", "screenshot_filename": "ee5KkVz90e.png", "report_screenshot": "ee5KkVz90e.png", "resolved_screenshot": "screenshots/ee5KkVz90e.png", "clean_action_id": "ee5KkVz90e", "prefixed_action_id": "al_ee5KkVz90e", "action_id_screenshot": "screenshots/ee5KkVz90e.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report"}, {"name": "Add Log: App is closed (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            10 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "failed", "duration": "0ms", "action_id": "To6rgFtm9R", "screenshot_filename": "To6rgFtm9R.png", "report_screenshot": "To6rgFtm9R.png", "resolved_screenshot": "screenshots/To6rgFtm9R.png", "clean_action_id": "To6rgFtm9R", "prefixed_action_id": "al_To6rgFtm9R", "action_id_screenshot": "screenshots/To6rgFtm9R.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "takeScreenshot action", "status": "unknown", "duration": "0ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "ee5KkVz90e", "screenshot_filename": "ee5KkVz90e.png", "report_screenshot": "ee5KkVz90e.png", "resolved_screenshot": "screenshots/ee5KkVz90e.png", "clean_action_id": "ee5KkVz90e", "prefixed_action_id": "al_ee5KkVz90e", "action_id_screenshot": "screenshots/ee5KkVz90e.png"}, {"name": "takeScreenshot action", "status": "unknown", "duration": "0ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}], "passed": 0, "failed": 2, "skipped": 0, "status": "failed"}