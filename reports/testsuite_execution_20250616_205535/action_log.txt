Action Log - 2025-06-16 20:56:43
================================================================================

[[20:56:42]] [INFO] Generating execution report...
[[20:56:42]] [SUCCESS] All tests passed successfully!
[[20:56:42]] [SUCCESS] Screenshot refreshed
[[20:56:42]] [INFO] Refreshing screenshot...
[[20:56:40]] [SUCCESS] Screenshot refreshed successfully
[[20:56:40]] [SUCCESS] Screenshot refreshed successfully
[[20:56:40]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[20:56:39]] [SUCCESS] Screenshot refreshed
[[20:56:39]] [INFO] Refreshing screenshot...
[[20:56:37]] [SUCCESS] Screenshot refreshed successfully
[[20:56:37]] [SUCCESS] Screenshot refreshed successfully
[[20:56:36]] [INFO] Executing action 18/19: takeScreenshot action
[[20:56:35]] [SUCCESS] Screenshot refreshed
[[20:56:35]] [INFO] Refreshing screenshot...
[[20:56:32]] [SUCCESS] Screenshot refreshed successfully
[[20:56:32]] [SUCCESS] Screenshot refreshed successfully
[[20:56:32]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[20:56:32]] [SUCCESS] Screenshot refreshed
[[20:56:32]] [INFO] Refreshing screenshot...
[[20:56:29]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:56:28]] [SUCCESS] Screenshot refreshed successfully
[[20:56:28]] [SUCCESS] Screenshot refreshed successfully
[[20:56:28]] [SUCCESS] Screenshot refreshed
[[20:56:28]] [INFO] Refreshing screenshot...
[[20:56:27]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:56:27]] [SUCCESS] Screenshot refreshed successfully
[[20:56:27]] [SUCCESS] Screenshot refreshed successfully
[[20:56:26]] [SUCCESS] Screenshot refreshed
[[20:56:26]] [INFO] Refreshing screenshot...
[[20:56:23]] [SUCCESS] Screenshot refreshed successfully
[[20:56:23]] [SUCCESS] Screenshot refreshed successfully
[[20:56:23]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:56:23]] [SUCCESS] Screenshot refreshed
[[20:56:23]] [INFO] Refreshing screenshot...
[[20:56:20]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:56:20]] [SUCCESS] Screenshot refreshed successfully
[[20:56:20]] [SUCCESS] Screenshot refreshed successfully
[[20:56:19]] [SUCCESS] Screenshot refreshed
[[20:56:19]] [INFO] Refreshing screenshot...
[[20:56:17]] [INFO] Executing action 12/19: takeScreenshot action
[[20:56:17]] [SUCCESS] Screenshot refreshed successfully
[[20:56:17]] [SUCCESS] Screenshot refreshed successfully
[[20:56:17]] [SUCCESS] Screenshot refreshed
[[20:56:17]] [INFO] Refreshing screenshot...
[[20:56:14]] [SUCCESS] Screenshot refreshed successfully
[[20:56:14]] [SUCCESS] Screenshot refreshed successfully
[[20:56:14]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:56:14]] [SUCCESS] Screenshot refreshed
[[20:56:14]] [INFO] Refreshing screenshot...
[[20:56:02]] [SUCCESS] Screenshot refreshed successfully
[[20:56:02]] [SUCCESS] Screenshot refreshed successfully
[[20:56:01]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[20:56:01]] [SUCCESS] Screenshot refreshed
[[20:56:01]] [INFO] Refreshing screenshot...
[[20:55:59]] [SUCCESS] Screenshot refreshed successfully
[[20:55:59]] [SUCCESS] Screenshot refreshed successfully
[[20:55:58]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[20:55:58]] [SUCCESS] Screenshot refreshed
[[20:55:58]] [INFO] Refreshing screenshot...
[[20:55:55]] [SUCCESS] Screenshot refreshed successfully
[[20:55:55]] [SUCCESS] Screenshot refreshed successfully
[[20:55:55]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[20:55:54]] [SUCCESS] Screenshot refreshed
[[20:55:54]] [INFO] Refreshing screenshot...
[[20:55:52]] [SUCCESS] Screenshot refreshed successfully
[[20:55:52]] [SUCCESS] Screenshot refreshed successfully
[[20:55:52]] [INFO] Executing action 7/19: Wait for 1 ms
[[20:55:51]] [SUCCESS] Screenshot refreshed
[[20:55:51]] [INFO] Refreshing screenshot...
[[20:55:50]] [SUCCESS] Screenshot refreshed successfully
[[20:55:50]] [SUCCESS] Screenshot refreshed successfully
[[20:55:50]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[20:55:49]] [SUCCESS] Screenshot refreshed
[[20:55:49]] [INFO] Refreshing screenshot...
[[20:55:46]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:55:46]] [SUCCESS] Screenshot refreshed successfully
[[20:55:46]] [SUCCESS] Screenshot refreshed successfully
[[20:55:45]] [SUCCESS] Screenshot refreshed
[[20:55:45]] [INFO] Refreshing screenshot...
[[20:55:44]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[20:55:44]] [SUCCESS] Screenshot refreshed successfully
[[20:55:44]] [SUCCESS] Screenshot refreshed successfully
[[20:55:43]] [SUCCESS] Screenshot refreshed
[[20:55:43]] [INFO] Refreshing screenshot...
[[20:55:41]] [SUCCESS] Screenshot refreshed successfully
[[20:55:41]] [SUCCESS] Screenshot refreshed successfully
[[20:55:41]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:55:40]] [SUCCESS] Screenshot refreshed
[[20:55:40]] [INFO] Refreshing screenshot...
[[20:55:39]] [SUCCESS] Screenshot refreshed successfully
[[20:55:39]] [SUCCESS] Screenshot refreshed successfully
[[20:55:39]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[20:55:38]] [SUCCESS] Screenshot refreshed
[[20:55:38]] [INFO] Refreshing screenshot...
[[20:55:35]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[20:55:35]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[20:55:35]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[20:55:35]] [INFO] Clearing screenshots from database before execution...
[[20:55:35]] [SUCCESS] All screenshots deleted successfully
[[20:55:35]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:55:35]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_205535/screenshots
[[20:55:35]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_205535
[[20:55:35]] [SUCCESS] Report directory initialized successfully
[[20:55:35]] [INFO] Initializing report directory and screenshots folder...
[[20:55:33]] [SUCCESS] All screenshots deleted successfully
[[20:55:33]] [INFO] All actions cleared
[[20:55:33]] [INFO] Cleaning up screenshots...
[[20:55:30]] [SUCCESS] Screenshot refreshed successfully
[[20:55:29]] [SUCCESS] Screenshot refreshed
[[20:55:29]] [INFO] Refreshing screenshot...
[[20:55:28]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[20:55:28]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[20:55:25]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[20:55:24]] [SUCCESS] Found 1 device(s)
[[20:55:23]] [INFO] Refreshing device list...
