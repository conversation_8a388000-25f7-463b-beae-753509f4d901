{"name": "UI Execution 16/06/2025, 20:56:42", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1323ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "11ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1208ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "11ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "12ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1010ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1099ms", "action_id": "vjBGuN5y9x", "screenshot_filename": "vjBGuN5y9x.png", "report_screenshot": "vjBGuN5y9x.png", "resolved_screenshot": "screenshots/vjBGuN5y9x.png", "clean_action_id": "vjBGuN5y9x", "prefixed_action_id": "al_vjBGuN5y9x", "action_id_screenshot": "screenshots/vjBGuN5y9x.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "11ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            10 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1167ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1177ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "takeScreenshot action", "status": "passed", "duration": "429ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1590ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1252ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "duration": "13ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1631ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1034ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "takeScreenshot action", "status": "passed", "duration": "1116ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "13ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}], "passed": 2, "failed": 0, "skipped": 0, "status": "passed"}