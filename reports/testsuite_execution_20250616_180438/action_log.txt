Action Log - 2025-06-16 18:06:15
================================================================================

[[18:06:14]] [INFO] Generating execution report...
[[18:06:14]] [WARNING] 1 test failed.
[[18:06:14]] [INFO] Skipping remaining steps in failed test case (moving from action 19 to next test case at 27)
[[18:06:14]] [ERROR] Action 19 failed: Screenshot file not found after capture
[[18:06:13]] [INFO] Executing action 19/27: takeScreenshot action
[[18:06:13]] [SUCCESS] Screenshot refreshed successfully
[[18:06:13]] [SUCCESS] Screenshot refreshed successfully
[[18:06:12]] [SUCCESS] Screenshot refreshed
[[18:06:12]] [INFO] Refreshing screenshot...
[[18:06:10]] [SUCCESS] Screenshot refreshed successfully
[[18:06:10]] [SUCCESS] Screenshot refreshed successfully
[[18:06:10]] [INFO] Executing action 18/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:06:09]] [SUCCESS] Screenshot refreshed
[[18:06:09]] [INFO] Refreshing screenshot...
[[18:05:57]] [SUCCESS] Screenshot refreshed successfully
[[18:05:57]] [SUCCESS] Screenshot refreshed successfully
[[18:05:57]] [INFO] Executing action 17/27: Launch app: com.apple.Health
[[18:05:56]] [SUCCESS] Screenshot refreshed
[[18:05:56]] [INFO] Refreshing screenshot...
[[18:05:56]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[18:05:56]] [INFO] Executing action 16/27: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[18:05:56]] [SUCCESS] Screenshot refreshed
[[18:05:56]] [INFO] Refreshing screenshot...
[[18:05:56]] [SUCCESS] Screenshot refreshed
[[18:05:56]] [INFO] Refreshing screenshot...
[[18:05:54]] [SUCCESS] Screenshot refreshed successfully
[[18:05:54]] [SUCCESS] Screenshot refreshed successfully
[[18:05:53]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[18:05:53]] [SUCCESS] Screenshot refreshed
[[18:05:53]] [INFO] Refreshing screenshot...
[[18:05:50]] [SUCCESS] Screenshot refreshed successfully
[[18:05:50]] [SUCCESS] Screenshot refreshed successfully
[[18:05:50]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[18:05:49]] [SUCCESS] Screenshot refreshed
[[18:05:49]] [INFO] Refreshing screenshot...
[[18:05:47]] [SUCCESS] Screenshot refreshed successfully
[[18:05:47]] [SUCCESS] Screenshot refreshed successfully
[[18:05:47]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[18:05:46]] [SUCCESS] Screenshot refreshed
[[18:05:46]] [INFO] Refreshing screenshot...
[[18:05:45]] [SUCCESS] Screenshot refreshed successfully
[[18:05:45]] [SUCCESS] Screenshot refreshed successfully
[[18:05:45]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[18:05:44]] [SUCCESS] Screenshot refreshed
[[18:05:44]] [INFO] Refreshing screenshot...
[[18:05:41]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:05:41]] [SUCCESS] Screenshot refreshed successfully
[[18:05:41]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [SUCCESS] Screenshot refreshed
[[18:05:40]] [INFO] Refreshing screenshot...
[[18:05:39]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[18:05:39]] [SUCCESS] Screenshot refreshed successfully
[[18:05:39]] [SUCCESS] Screenshot refreshed successfully
[[18:05:38]] [SUCCESS] Screenshot refreshed
[[18:05:38]] [INFO] Refreshing screenshot...
[[18:05:36]] [SUCCESS] Screenshot refreshed successfully
[[18:05:36]] [SUCCESS] Screenshot refreshed successfully
[[18:05:36]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:05:35]] [SUCCESS] Screenshot refreshed
[[18:05:35]] [INFO] Refreshing screenshot...
[[18:05:34]] [SUCCESS] Screenshot refreshed successfully
[[18:05:34]] [SUCCESS] Screenshot refreshed successfully
[[18:05:34]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[18:05:33]] [SUCCESS] Screenshot refreshed
[[18:05:33]] [INFO] Refreshing screenshot...
[[18:05:29]] [SUCCESS] Screenshot refreshed successfully
[[18:05:29]] [SUCCESS] Screenshot refreshed successfully
[[18:05:28]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[18:05:28]] [INFO] Loaded 9 steps from test case: health2
[[18:05:28]] [INFO] Loading steps for Multi Step action: health2
[[18:05:28]] [INFO] Executing action 15/27: Execute Test Case: health2 (9 steps)
[[18:05:28]] [SUCCESS] Screenshot refreshed
[[18:05:28]] [INFO] Refreshing screenshot...
[[18:05:26]] [SUCCESS] Screenshot refreshed successfully
[[18:05:26]] [SUCCESS] Screenshot refreshed successfully
[[18:05:26]] [INFO] Executing action 14/27: Add Log: Closed App Successfully (with screenshot)
[[18:05:25]] [SUCCESS] Screenshot refreshed
[[18:05:25]] [INFO] Refreshing screenshot...
[[18:05:22]] [SUCCESS] Screenshot refreshed successfully
[[18:05:22]] [SUCCESS] Screenshot refreshed successfully
[[18:05:22]] [INFO] Executing action 13/27: Terminate app: com.apple.Health
[[18:05:21]] [SUCCESS] Screenshot refreshed
[[18:05:21]] [INFO] Refreshing screenshot...
[[18:05:18]] [INFO] Executing action 12/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:05:18]] [SUCCESS] Screenshot refreshed successfully
[[18:05:18]] [SUCCESS] Screenshot refreshed successfully
[[18:05:18]] [SUCCESS] Screenshot refreshed
[[18:05:18]] [INFO] Refreshing screenshot...
[[18:05:16]] [SUCCESS] Screenshot refreshed successfully
[[18:05:16]] [SUCCESS] Screenshot refreshed successfully
[[18:05:16]] [INFO] Executing action 11/27: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[18:05:15]] [SUCCESS] Screenshot refreshed
[[18:05:15]] [INFO] Refreshing screenshot...
[[18:05:03]] [SUCCESS] Screenshot refreshed successfully
[[18:05:03]] [SUCCESS] Screenshot refreshed successfully
[[18:05:02]] [INFO] Executing action 10/27: Launch app: com.apple.Health
[[18:05:02]] [SUCCESS] Screenshot refreshed
[[18:05:02]] [INFO] Refreshing screenshot...
[[18:05:00]] [SUCCESS] Screenshot refreshed successfully
[[18:05:00]] [SUCCESS] Screenshot refreshed successfully
[[18:05:00]] [INFO] Executing action 9/27: Add Log: App is closed (with screenshot)
[[18:04:59]] [SUCCESS] Screenshot refreshed
[[18:04:59]] [INFO] Refreshing screenshot...
[[18:04:56]] [SUCCESS] Screenshot refreshed successfully
[[18:04:56]] [SUCCESS] Screenshot refreshed successfully
[[18:04:56]] [INFO] Executing action 8/27: Terminate app: com.apple.Health
[[18:04:55]] [SUCCESS] Screenshot refreshed
[[18:04:55]] [INFO] Refreshing screenshot...
[[18:04:53]] [SUCCESS] Screenshot refreshed successfully
[[18:04:53]] [SUCCESS] Screenshot refreshed successfully
[[18:04:53]] [INFO] Executing action 7/27: Wait for 1 ms
[[18:04:52]] [SUCCESS] Screenshot refreshed
[[18:04:52]] [INFO] Refreshing screenshot...
[[18:04:51]] [SUCCESS] Screenshot refreshed successfully
[[18:04:51]] [SUCCESS] Screenshot refreshed successfully
[[18:04:51]] [INFO] Executing action 6/27: Add Log: Done link is clicked (with screenshot)
[[18:04:50]] [SUCCESS] Screenshot refreshed
[[18:04:50]] [INFO] Refreshing screenshot...
[[18:04:47]] [INFO] Executing action 5/27: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[18:04:47]] [SUCCESS] Screenshot refreshed successfully
[[18:04:47]] [SUCCESS] Screenshot refreshed successfully
[[18:04:47]] [SUCCESS] Screenshot refreshed
[[18:04:47]] [INFO] Refreshing screenshot...
[[18:04:45]] [INFO] Executing action 4/27: Add Log: Edit link is clicked (with screenshot)
[[18:04:45]] [SUCCESS] Screenshot refreshed successfully
[[18:04:45]] [SUCCESS] Screenshot refreshed successfully
[[18:04:45]] [SUCCESS] Screenshot refreshed
[[18:04:45]] [INFO] Refreshing screenshot...
[[18:04:42]] [SUCCESS] Screenshot refreshed successfully
[[18:04:42]] [SUCCESS] Screenshot refreshed successfully
[[18:04:42]] [INFO] Executing action 3/27: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[18:04:41]] [SUCCESS] Screenshot refreshed
[[18:04:41]] [INFO] Refreshing screenshot...
[[18:04:40]] [SUCCESS] Screenshot refreshed successfully
[[18:04:40]] [SUCCESS] Screenshot refreshed successfully
[[18:04:40]] [INFO] Executing action 2/27: Add Log: Launched App Successfully (with screenshot)
[[18:04:39]] [SUCCESS] Screenshot refreshed
[[18:04:39]] [INFO] Refreshing screenshot...
[[18:04:38]] [INFO] Executing action 1/27: Launch app: com.apple.Health
[[18:04:38]] [INFO] ExecutionManager: Starting execution of 27 actions...
[[18:04:38]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[18:04:38]] [INFO] Clearing screenshots from database before execution...
[[18:04:38]] [SUCCESS] All screenshots deleted successfully
[[18:04:38]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:04:38]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_180438/screenshots
[[18:04:38]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_180438
[[18:04:38]] [SUCCESS] Report directory initialized successfully
[[18:04:37]] [INFO] Initializing report directory and screenshots folder...
[[18:04:36]] [SUCCESS] All screenshots deleted successfully
[[18:04:36]] [INFO] All actions cleared
[[18:04:36]] [INFO] Cleaning up screenshots...
[[18:04:30]] [SUCCESS] Screenshot refreshed successfully
[[18:04:29]] [SUCCESS] Screenshot refreshed
[[18:04:29]] [INFO] Refreshing screenshot...
[[18:04:28]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:04:28]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:04:24]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:04:20]] [SUCCESS] Found 1 device(s)
[[18:04:19]] [INFO] Refreshing device list...
