{"name": "UI Execution 16/06/2025, 18:06:14", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "187ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "14ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1281ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "13ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1633ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "13ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1009ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1107ms", "action_id": "HphRLWPfSD", "screenshot_filename": "HphRLWPfSD.png", "report_screenshot": "HphRLWPfSD.png", "resolved_screenshot": "screenshots/HphRLWPfSD.png", "clean_action_id": "HphRLWPfSD", "prefixed_action_id": "al_HphRLWPfSD", "action_id_screenshot": "screenshots/HphRLWPfSD.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "12ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1177ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "893ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1444ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1125ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: Closed App Successfully (with screenshot)", "status": "passed", "duration": "12ms", "action_id": "Successful", "screenshot_filename": "Successful.png", "report_screenshot": "Successful.png", "resolved_screenshot": "screenshots/Successful.png"}, {"name": "Execute Test Case: health2 (9 steps)", "status": "passed", "duration": "0ms", "action_id": "HphRLWPfSD", "screenshot_filename": "HphRLWPfSD.png", "report_screenshot": "HphRLWPfSD.png", "resolved_screenshot": "screenshots/HphRLWPfSD.png", "clean_action_id": "HphRLWPfSD", "prefixed_action_id": "al_HphRLWPfSD", "action_id_screenshot": "screenshots/HphRLWPfSD.png"}, {"name": "Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)", "status": "passed", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            11 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1171ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1229ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "takeScreenshot action", "status": "failed", "duration": "0ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "clean_action_id": "4kBvNvFi5i", "prefixed_action_id": "al_4kBvNvFi5i", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "takeScreenshot action", "status": "unknown", "duration": "0ms", "action_id": "takeScreen", "screenshot_filename": "takeScreen.png", "report_screenshot": "takeScreen.png", "resolved_screenshot": "screenshots/takeScreen.png"}, {"name": "Execute Test Case: apple health (8 steps)", "status": "unknown", "duration": "0ms", "action_id": "HphRLWPfSD", "screenshot_filename": "HphRLWPfSD.png", "report_screenshot": "HphRLWPfSD.png", "resolved_screenshot": "screenshots/HphRLWPfSD.png", "clean_action_id": "HphRLWPfSD", "prefixed_action_id": "al_HphRLWPfSD", "action_id_screenshot": "screenshots/HphRLWPfSD.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "screenshot", "screenshot_filename": "screenshot.png", "report_screenshot": "screenshot.png", "resolved_screenshot": "screenshots/screenshot.png"}]}], "passed": 2, "failed": 1, "skipped": 0, "status": "failed"}