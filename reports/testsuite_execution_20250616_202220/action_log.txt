Action Log - 2025-06-16 20:23:28
================================================================================

[[20:23:27]] [INFO] Generating execution report...
[[20:23:27]] [SUCCESS] All tests passed successfully!
[[20:23:27]] [SUCCESS] Screenshot refreshed
[[20:23:27]] [INFO] Refreshing screenshot...
[[20:23:25]] [SUCCESS] Screenshot refreshed successfully
[[20:23:25]] [SUCCESS] Screenshot refreshed successfully
[[20:23:25]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[20:23:24]] [SUCCESS] Screenshot refreshed
[[20:23:24]] [INFO] Refreshing screenshot...
[[20:23:22]] [SUCCESS] Screenshot refreshed successfully
[[20:23:22]] [SUCCESS] Screenshot refreshed successfully
[[20:23:21]] [INFO] Executing action 18/19: takeScreenshot action
[[20:23:21]] [SUCCESS] Screenshot refreshed
[[20:23:21]] [INFO] Refreshing screenshot...
[[20:23:17]] [SUCCESS] Screenshot refreshed successfully
[[20:23:17]] [SUCCESS] Screenshot refreshed successfully
[[20:23:17]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[20:23:17]] [SUCCESS] Screenshot refreshed
[[20:23:17]] [INFO] Refreshing screenshot...
[[20:23:14]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:23:14]] [SUCCESS] Screenshot refreshed successfully
[[20:23:14]] [SUCCESS] Screenshot refreshed successfully
[[20:23:13]] [SUCCESS] Screenshot refreshed
[[20:23:13]] [INFO] Refreshing screenshot...
[[20:23:12]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:23:12]] [SUCCESS] Screenshot refreshed successfully
[[20:23:12]] [SUCCESS] Screenshot refreshed successfully
[[20:23:11]] [SUCCESS] Screenshot refreshed
[[20:23:11]] [INFO] Refreshing screenshot...
[[20:23:09]] [SUCCESS] Screenshot refreshed successfully
[[20:23:09]] [SUCCESS] Screenshot refreshed successfully
[[20:23:09]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:23:08]] [SUCCESS] Screenshot refreshed
[[20:23:08]] [INFO] Refreshing screenshot...
[[20:23:05]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:23:05]] [SUCCESS] Screenshot refreshed successfully
[[20:23:05]] [SUCCESS] Screenshot refreshed successfully
[[20:23:04]] [SUCCESS] Screenshot refreshed
[[20:23:04]] [INFO] Refreshing screenshot...
[[20:23:03]] [INFO] Executing action 12/19: takeScreenshot action
[[20:23:03]] [SUCCESS] Screenshot refreshed successfully
[[20:23:03]] [SUCCESS] Screenshot refreshed successfully
[[20:23:02]] [SUCCESS] Screenshot refreshed
[[20:23:02]] [INFO] Refreshing screenshot...
[[20:23:00]] [SUCCESS] Screenshot refreshed successfully
[[20:23:00]] [SUCCESS] Screenshot refreshed successfully
[[20:22:59]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:22:59]] [SUCCESS] Screenshot refreshed
[[20:22:59]] [INFO] Refreshing screenshot...
[[20:22:47]] [SUCCESS] Screenshot refreshed successfully
[[20:22:47]] [SUCCESS] Screenshot refreshed successfully
[[20:22:46]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[20:22:46]] [SUCCESS] Screenshot refreshed
[[20:22:46]] [INFO] Refreshing screenshot...
[[20:22:44]] [SUCCESS] Screenshot refreshed successfully
[[20:22:44]] [SUCCESS] Screenshot refreshed successfully
[[20:22:44]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[20:22:43]] [SUCCESS] Screenshot refreshed
[[20:22:43]] [INFO] Refreshing screenshot...
[[20:22:40]] [SUCCESS] Screenshot refreshed successfully
[[20:22:40]] [SUCCESS] Screenshot refreshed successfully
[[20:22:40]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[20:22:39]] [SUCCESS] Screenshot refreshed
[[20:22:39]] [INFO] Refreshing screenshot...
[[20:22:37]] [SUCCESS] Screenshot refreshed successfully
[[20:22:37]] [SUCCESS] Screenshot refreshed successfully
[[20:22:37]] [INFO] Executing action 7/19: Wait for 1 ms
[[20:22:36]] [SUCCESS] Screenshot refreshed
[[20:22:36]] [INFO] Refreshing screenshot...
[[20:22:35]] [SUCCESS] Screenshot refreshed successfully
[[20:22:35]] [SUCCESS] Screenshot refreshed successfully
[[20:22:35]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[20:22:34]] [SUCCESS] Screenshot refreshed
[[20:22:34]] [INFO] Refreshing screenshot...
[[20:22:31]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:22:31]] [SUCCESS] Screenshot refreshed successfully
[[20:22:31]] [SUCCESS] Screenshot refreshed successfully
[[20:22:31]] [SUCCESS] Screenshot refreshed
[[20:22:31]] [INFO] Refreshing screenshot...
[[20:22:29]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[20:22:29]] [SUCCESS] Screenshot refreshed successfully
[[20:22:29]] [SUCCESS] Screenshot refreshed successfully
[[20:22:29]] [SUCCESS] Screenshot refreshed
[[20:22:29]] [INFO] Refreshing screenshot...
[[20:22:26]] [SUCCESS] Screenshot refreshed successfully
[[20:22:26]] [SUCCESS] Screenshot refreshed successfully
[[20:22:26]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:22:25]] [SUCCESS] Screenshot refreshed
[[20:22:25]] [INFO] Refreshing screenshot...
[[20:22:24]] [SUCCESS] Screenshot refreshed successfully
[[20:22:24]] [SUCCESS] Screenshot refreshed successfully
[[20:22:24]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[20:22:23]] [SUCCESS] Screenshot refreshed
[[20:22:23]] [INFO] Refreshing screenshot...
[[20:22:20]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[20:22:20]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[20:22:20]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[20:22:20]] [INFO] Clearing screenshots from database before execution...
[[20:22:20]] [SUCCESS] All screenshots deleted successfully
[[20:22:20]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:22:20]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_202220/screenshots
[[20:22:20]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_202220
[[20:22:20]] [SUCCESS] Report directory initialized successfully
[[20:22:20]] [INFO] Initializing report directory and screenshots folder...
[[20:22:16]] [SUCCESS] All screenshots deleted successfully
[[20:22:16]] [INFO] All actions cleared
[[20:22:16]] [INFO] Cleaning up screenshots...
[[20:22:13]] [SUCCESS] Screenshot refreshed successfully
[[20:22:12]] [SUCCESS] Screenshot refreshed
[[20:22:12]] [INFO] Refreshing screenshot...
[[20:22:11]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[20:22:11]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[20:22:08]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[20:22:06]] [SUCCESS] Found 1 device(s)
[[20:22:06]] [INFO] Refreshing device list...
