#!/usr/bin/env python3

import os
import sys
import json
import tempfile
import shutil

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.custom_report_generator import CustomReportGenerator

def test_custom_report_generator():
    """Test the custom report generator with takeScreenshot actions"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    print(f"Test directory: {test_dir}")
    
    try:
        # Create test data
        test_data = {
            "testCases": [
                {
                    "name": "Test Case 1",
                    "status": "success",
                    "steps": [
                        {
                            "action_id": "abc1234567",
                            "name": "tap action",
                            "description": "Click element: xpath=//button[@name='Submit']",
                            "type": "tap"
                        },
                        {
                            "action_id": "def2345678",
                            "name": "takeScreenshot action",
                            "description": "takeScreenshot action",
                            "type": "takeScreenshot",
                            "screenshot_name": "my_custom_screenshot"
                        },
                        {
                            "action_id": "ghi3456789",
                            "name": "text action",
                            "description": "Input text: Hello World",
                            "type": "text"
                        }
                    ]
                }
            ]
        }
        
        # Create test report directory structure
        report_dir = os.path.join(test_dir, "test_report")
        os.makedirs(report_dir)
        
        # Create data.json file
        data_file = os.path.join(report_dir, "data.json")
        with open(data_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        # Create screenshots directory and dummy screenshots
        screenshots_dir = os.path.join(report_dir, "screenshots")
        os.makedirs(screenshots_dir)
        
        # Create dummy screenshot files
        for action_id in ["abc1234567", "def2345678", "ghi3456789"]:
            screenshot_file = os.path.join(screenshots_dir, f"{action_id}.png")
            with open(screenshot_file, 'w') as f:
                f.write("dummy screenshot")
        
        # Create custom screenshot file
        custom_screenshot_file = os.path.join(screenshots_dir, "my_custom_screenshot.png")
        with open(custom_screenshot_file, 'w') as f:
            f.write("custom screenshot")
        
        # Test the custom report generator
        # Create the report in the actual reports directory
        app_root = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest"
        reports_dir = os.path.join(app_root, "reports")

        # Copy our test report to the reports directory
        actual_report_dir = os.path.join(reports_dir, "test_report")
        if os.path.exists(actual_report_dir):
            shutil.rmtree(actual_report_dir)
        shutil.copytree(report_dir, actual_report_dir)

        generator = CustomReportGenerator("test_report", app_root)
        
        # Process the test data
        print("Input test data:")
        print(json.dumps(test_data, indent=2))

        processed_data = generator._process_test_data(test_data)
        
        print("Processed data:")
        print(json.dumps(processed_data, indent=2))
        
        # Check that takeScreenshot action has the custom screenshot
        test_case = processed_data['test_cases'][0]
        actions = test_case['actions']
        
        print("\nAction screenshot assignments:")
        for action in actions:
            action_type = action.get('type', 'unknown')
            screenshot = action.get('screenshot', 'none')
            description = action.get('description', '')
            print(f"  {action_type}: {screenshot} - {description}")
        
        # Verify that takeScreenshot action uses custom screenshot
        takescreenshot_action = None
        for action in actions:
            if 'takescreenshot' in action.get('description', '').lower():
                takescreenshot_action = action
                break
        
        if takescreenshot_action:
            expected_screenshot = "screenshots/my_custom_screenshot.png"
            actual_screenshot = takescreenshot_action.get('screenshot', '')
            if actual_screenshot == expected_screenshot:
                print(f"\n✅ SUCCESS: takeScreenshot action uses custom screenshot: {actual_screenshot}")
            else:
                print(f"\n❌ FAILURE: takeScreenshot action should use {expected_screenshot}, but got {actual_screenshot}")
        else:
            print("\n❌ FAILURE: takeScreenshot action not found")
        
        # Generate the actual report
        success = generator.generate_report()
        if success:
            print(f"\n✅ Report generated successfully")
            
            # Check if the HTML file was created
            html_file = os.path.join(generator.export_dir, "test_execution_report.html")
            if os.path.exists(html_file):
                print(f"✅ HTML report created: {html_file}")
                
                # Read and check the HTML content
                with open(html_file, 'r') as f:
                    html_content = f.read()
                
                # Check for takeScreenshot condition in template
                if 'takescreenshot' in html_content.lower():
                    print("✅ HTML contains takeScreenshot condition")
                else:
                    print("❌ HTML does not contain takeScreenshot condition")
                    
            else:
                print(f"❌ HTML report not found: {html_file}")
        else:
            print(f"\n❌ Report generation failed")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        shutil.rmtree(test_dir)
        print(f"\nCleaned up test directory: {test_dir}")

        # Clean up the actual report directory
        try:
            app_root = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest"
            reports_dir = os.path.join(app_root, "reports")
            actual_report_dir = os.path.join(reports_dir, "test_report")
            if os.path.exists(actual_report_dir):
                shutil.rmtree(actual_report_dir)
                print(f"Cleaned up actual report directory: {actual_report_dir}")
        except:
            pass

if __name__ == "__main__":
    test_custom_report_generator()
