#!/usr/bin/env python3

import sys
import os

# Add the app directory to the Python path
sys.path.append('app')

def test_take_screenshot_action():
    """Test the takeScreenshot action directly"""
    
    print("Testing takeScreenshot action...")
    
    try:
        from actions.action_factory import ActionFactory
        
        # Create action factory instance
        factory = ActionFactory()
        
        # Test parameters
        test_params = {
            'screenshot_name': 'test_screenshot'
        }
        
        print(f"Testing with parameters: {test_params}")
        
        # Execute the takeScreenshot action
        result = factory.execute_action('takeScreenshot', test_params)
        
        print(f"Execution result: {result}")
        
        # Check if screenshot files were created
        expected_files = [
            'test_screenshot.png',
            'app-screenshot_test_screenshot.png'
        ]
        
        for filename in expected_files:
            if os.path.exists(filename):
                print(f"✅ SUCCESS: Screenshot file created: {filename}")
            else:
                print(f"❌ ERROR: Screenshot file not found: {filename}")
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_take_screenshot_action()
