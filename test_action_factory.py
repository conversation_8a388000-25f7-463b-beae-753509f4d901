#!/usr/bin/env python3

import sys
import os

# Add the app directory to the Python path
sys.path.append('app')

def test_action_factory():
    """Test if the action factory can discover and register the takeScreenshot action"""
    
    print("Testing action factory...")
    
    try:
        from actions.action_factory import ActionFactory
        
        # Create action factory instance
        factory = ActionFactory()
        
        # Get all registered actions
        actions = list(factory.action_handlers.keys())

        print(f"Total actions registered: {len(actions)}")
        print("Registered actions:")
        for action in sorted(actions):
            print(f"  - {action}")

        # Check if takeScreenshot is registered
        if 'takeScreenshot' in actions:
            print("\n✅ SUCCESS: takeScreenshot action is registered!")
            
            # Try to get the instance
            try:
                action_instance = factory.action_handlers['takeScreenshot']
                print(f"✅ SUCCESS: Can get takeScreenshot instance: {action_instance}")
                print(f"Action class: {action_instance.__class__.__name__}")
            except Exception as e:
                print(f"❌ ERROR: Cannot get takeScreenshot instance: {e}")
        else:
            print("\n❌ ERROR: takeScreenshot action is NOT registered!")
            
        # Check if the file exists
        take_screenshot_file = 'app/actions/take_screenshot_action.py'
        if os.path.exists(take_screenshot_file):
            print(f"✅ File exists: {take_screenshot_file}")
        else:
            print(f"❌ File missing: {take_screenshot_file}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_action_factory()
